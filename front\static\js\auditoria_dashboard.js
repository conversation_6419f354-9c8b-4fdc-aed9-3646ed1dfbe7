/**
 * auditoria_dashboard.js - Funções para o dashboard de auditoria
 */

console.log('Carregando auditoria_dashboard.js');

// Cache para dados de detalhamento e opções de filtros
let detalhamentoCache = new Map();
let filtroOptionsCache = new Map();
let debounceTimers = new Map();

// Mapa para armazenar os relacionamentos entre CFOP e NCM
const cfopNcmRelations = new Map();

/**
 * Carrega os relacionamentos entre CFOP e NCM do backend
 */
function carregarRelacionamentosCfopNcm() {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) return Promise.resolve();

  const url = `http://127.0.0.1:5000/api/auditoria/relacionamentos/cfop-ncm?empresa_id=${empresaId}&year=${year}&month=${month}`;

  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success && data.relacionamentos) {
        // Limpar relacionamentos existentes
        cfopNcmRelations.clear();

        // Preencher o mapa de relacionamentos
        data.relacionamentos.forEach((rel) => {
          if (!cfopNcmRelations.has(rel.cfop)) {
            cfopNcmRelations.set(rel.cfop, new Set());
          }
          cfopNcmRelations.get(rel.cfop).add(rel.ncm);
        });

        console.log('Relacionamentos CFOP-NCM carregados:', cfopNcmRelations);
        return true;
      }
      return false;
    })
    .catch((error) => {
      console.error('Erro ao carregar relacionamentos CFOP-NCM:', error);
      return false;
    });
}

/**
 * Carrega os dados do dashboard de auditoria
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 */
function carregarDashboardAuditoria(tipoTributo) {
  // Limpar cache ao carregar novo dashboard
  limparCacheDetalhamento();
  // Mostrar loading
  showLoading();

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    hideLoading();
    showErrorMessage(
      'Selecione uma empresa para visualizar o dashboard de auditoria.',
    );
    return;
  }

  // Construir URL com filtros
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}`;

  // Buscar dados do dashboard
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success) {
        // Renderizar dashboard
        renderizarDashboardAuditoria(data.dashboard, tipoTributo);
      } else {
        // Mostrar mensagem de erro
        const errorMsg = console.error(
          'Erro retornado pelo servidor:',
          errorMsg,
        );
        showErrorMessage(errorMsg);

        // Se não houver dados, mostrar dashboard vazio
        renderizarDashboardVazio(tipoTributo);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      console.error('Erro ao carregar dashboard de auditoria:', error);

      // Mostrar dashboard vazio
      renderizarDashboardVazio(tipoTributo);
    });
}

/**
 * Renderiza o dashboard de auditoria
 * @param {Object} dashboard - Dados do dashboard
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDashboardAuditoria(dashboard, tipoTributo) {
  const sumario = dashboard.sumario;
  const resultadosInconsistentes = dashboard.resultados_inconsistentes;

  // Obter o container do dashboard
  const dashboardContainer = document.getElementById(
    'auditoria-dashboard-container',
  );
  if (!dashboardContainer) {
    console.error('Container do dashboard não encontrado');
    return;
  }

  // Limpar o container
  dashboardContainer.innerHTML = '';

  // Criar sistema de tabs
  const tabsContainer = document.createElement('div');
  tabsContainer.className = 'dashboard-tabs-container';

  // Criar navegação das tabs
  const tabsNav = document.createElement('ul');
  tabsNav.className = 'nav nav-tabs mb-4';
  tabsNav.innerHTML = `
    <li class="nav-item">
      <a class="nav-link active" id="resumo-tab" data-bs-toggle="tab" href="#resumo" role="tab">
        <i class="fas fa-chart-pie"></i> Resumo
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" id="detalhamento-tab" data-bs-toggle="tab" href="#detalhamento" role="tab">
        <i class="fas fa-table"></i> Detalhamento
      </a>
    </li>
  `;

  // Criar conteúdo das tabs
  const tabsContent = document.createElement('div');
  tabsContent.className = 'tab-content';

  // Tab Resumo
  const resumoTab = document.createElement('div');
  resumoTab.className = 'tab-pane fade show active';
  resumoTab.id = 'resumo';
  resumoTab.setAttribute('role', 'tabpanel');

  // Tab Detalhamento
  const detalhamentoTab = document.createElement('div');
  detalhamentoTab.className = 'tab-pane fade';
  detalhamentoTab.id = 'detalhamento';
  detalhamentoTab.setAttribute('role', 'tabpanel');

  tabsContent.appendChild(resumoTab);
  tabsContent.appendChild(detalhamentoTab);

  tabsContainer.appendChild(tabsNav);
  tabsContainer.appendChild(tabsContent);
  dashboardContainer.appendChild(tabsContainer);

  // Renderizar conteúdo da tab Resumo
  renderizarTabResumo(resumoTab, sumario, resultadosInconsistentes);

  // Configurar evento para carregar detalhamento quando a tab for ativada
  const detalhamentoTabLink = document.getElementById('detalhamento-tab');
  detalhamentoTabLink.addEventListener('shown.bs.tab', function () {
    carregarDetalhamentoAuditoria(tipoTributo, detalhamentoTab);
  });
}

/**
 * Renderiza o conteúdo da tab Resumo
 * @param {HTMLElement} container - Container da tab
 * @param {Object} sumario - Dados do sumário
 * @param {Array} resultadosInconsistentes - Resultados inconsistentes
 */
function renderizarTabResumo(container, sumario, resultadosInconsistentes) {
  // Criar os cards principais
  const cardsRow = document.createElement('div');
  cardsRow.className = 'row mb-4';

  // Card de Notas
  cardsRow.innerHTML += `
    <div class="col-md-3">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Notas</h5>
          <div class="card-content">
            <div class="card-value">${formatCurrency(
              sumario.valor_total_notas,
            )}</div>
            <div class="card-subtitle">Total: ${sumario.total_notas} notas</div>
            <div class="card-subtitle text-muted">Valor dos tributos nas notas</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Cenários
  cardsRow.innerHTML += `
    <div class="col-md-3">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Cenários</h5>
          <div class="card-content">
            <div class="card-value">${formatCurrency(
              sumario.valor_total_cenarios,
            )}</div>
            <div class="card-subtitle">Total: ${
              sumario.total_produtos
            } itens</div>
            <div class="card-subtitle text-muted">Valor calculado pelos cenários</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Conformidade
  const valorConforme =
    sumario.valor_total_notas -
    sumario.valor_inconsistente_maior -
    sumario.valor_inconsistente_menor;
  cardsRow.innerHTML += `
    <div class="col-md-3">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Conformidade</h5>
          <div class="card-content">
            <div class="card-value text-success">${formatCurrency(
              valorConforme,
            )}</div>
            <div class="card-subtitle">Conforme: ${
              sumario.total_conforme
            } itens</div>
            <div class="card-subtitle text-muted">Valores que batem</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Inconsistência
  const totalInconsistente =
    sumario.valor_inconsistente_maior + sumario.valor_inconsistente_menor;
  const inconsistenciaColorClass = totalInconsistente > 0 ? 'text-danger' : '';
  cardsRow.innerHTML += `
    <div class="col-md-3">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Inconsistência</h5>
          <div class="card-content">
            <div class="card-value ${inconsistenciaColorClass}">${formatCurrency(
    totalInconsistente,
  )}</div>
            <div class="card-subtitle">Inconsistente: ${
              sumario.total_inconsistente
            } itens</div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(cardsRow);

  // Criar os cards de valores a maior e a menor
  const valoresRow = document.createElement('div');
  valoresRow.className = 'row mb-4';

  // Card de Valores a Maior
  const valorMaiorColorClass =
    sumario.valor_inconsistente_maior > 0 ? 'text-danger' : '';
  valoresRow.innerHTML += `
    <div class="col-md-6">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Valores a Maior</h5>
          <div class="card-content">
            <div class="card-value ${valorMaiorColorClass}">${formatCurrency(
    sumario.valor_inconsistente_maior,
  )}</div>
            <div class="card-subtitle">Valores calculados maiores que os da nota</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Valores a Menor
  const valorMenorColorClass =
    sumario.valor_inconsistente_menor > 0 ? 'text-danger' : '';
  valoresRow.innerHTML += `
    <div class="col-md-6">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Valores a Menor</h5>
          <div class="card-content">
            <div class="card-value ${valorMenorColorClass}">${formatCurrency(
    sumario.valor_inconsistente_menor,
  )}</div>
            <div class="card-subtitle">Valores calculados menores que os da nota</div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(valoresRow);

  // Criar resumo de inconsistências
  if (resultadosInconsistentes.length > 0) {
    const totalInconsistentes =
      sumario.total_inconsistente || resultadosInconsistentes.length;
    const inconsistentesVistas = sumario.total_inconsistentes_vistas || 0;
    const inconsistentesNaoVistas =
      sumario.total_inconsistentes_nao_vistas ||
      totalInconsistentes - inconsistentesVistas;

    const resumoInconsistencias = document.createElement('div');
    resumoInconsistencias.className = 'alert alert-warning mt-4';
    resumoInconsistencias.innerHTML = `
      <h5><i class="fas fa-exclamation-triangle"></i> Resumo de Inconsistências</h5>
      <p>Foram encontradas <strong>${totalInconsistentes}</strong> inconsistências neste período.</p>
      <div class="row mt-3">
        <div class="col-md-6">
          <div class="d-flex align-items-center">
            <i class="fas fa-eye text-success me-2"></i>
            <span><strong>${inconsistentesVistas}</strong> inconsistências foram analisadas pelo analista</span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex align-items-center">
            <i class="fas fa-eye-slash text-warning me-2"></i>
            <span><strong>${inconsistentesNaoVistas}</strong> inconsistências aguardam análise</span>
          </div>
        </div>
      </div>
      <p class="mt-3">Acesse a aba <strong>Detalhamento</strong> para visualizar e filtrar os dados completos.</p>
    `;
    container.appendChild(resumoInconsistencias);
  }
}

/**
 * Carrega os dados de detalhamento da auditoria
 * OTIMIZAÇÃO: Agora carrega apenas inconsistências por padrão para melhor performance
 * @param {string} tipoTributo - Tipo de tributo
 * @param {HTMLElement} container - Container da tab
 */
function carregarDetalhamentoAuditoria(tipoTributo, container) {
  // Verificar se já foi carregado
  if (container.hasAttribute('data-loaded')) {
    return;
  }

  // Mostrar loading
  container.innerHTML =
    '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Carregando detalhamento...</div>';

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    container.innerHTML =
      '<div class="alert alert-warning">Selecione uma empresa para visualizar o detalhamento.</div>';
    return;
  }

  // Construir URL com filtros - mostrar apenas inconsistências por padrão
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}&status=inconsistente`;

  // Buscar dados do detalhamento
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        renderizarDetalhamentoAuditoria(
          container,
          data.resultados,
          tipoTributo,
        );
        container.setAttribute('data-loaded', 'true');
      } else {
        container.innerHTML = `<div class="alert alert-danger">Erro ao carregar detalhamento: ${data.message}</div>`;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar detalhamento:', error);
      container.innerHTML = `<div class="alert alert-danger">Erro ao carregar detalhamento: ${error.message}</div>`;
    });
}

/**
 * Renderiza a tabela de detalhamento da auditoria
 * @param {HTMLElement} container - Container da tab
 * @param {Array} resultados - Dados dos resultados
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDetalhamentoAuditoria(container, resultados, tipoTributo) {
  container.innerHTML = '';

  // Criar filtros
  const filtrosContainer = document.createElement('div');
  filtrosContainer.className = 'audit-dashboard-filters';
  filtrosContainer.innerHTML = `
    <div class="row">
      <div class="col-md-2">
        <label class="form-label">Análise</label>
        <select class="form-select" id="filtro-analista-visualizou">
          <option value="">Todas</option>
          <option value="true">Analisadas</option>
          <option value="false">Não analisadas</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Atividade</label>
        <select class="form-select" id="filtro-atividade">
          <option value="">Todas</option>
          <option value="Indústria ou Equiparado">Indústria</option>
          <option value="Comércio Varejista">Comércio Varejista</option>
          <option value="Comércio Atacadista">Comércio Atacadista</option>
          <option value="Distribuidor">Distribuidor</option>
          <option value="Produtor Rural">Produtor Rural</option>
          <option value="Consumidor Final">Consumidor Final</option>
          <option value="Não Contribuinte">Não Contribuinte</option>
          <option value="Órgão Público">Órgão Público</option>
          <option value="Serviços">Serviços</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Destinação</label>
        <select class="form-select" id="filtro-destinacao">
          <option value="">Todas</option>
          <option value="Industrialização">Industrialização</option>
          <option value="Revenda">Revenda</option>
          <option value="Ativo Imobilizado">Ativo Imobilizado</option>
          <option value="Uso e Consumo">Uso e Consumo</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Número NF</label>
        <input type="text" class="form-control" id="filtro-numero" placeholder="Filtrar por número">
      </div>
      <div class="col-md-2">
        <label class="form-label">Produto</label>
        <input type="text" class="form-control" id="filtro-produto" placeholder="Filtrar por produto">
      </div>
    </div>

    <!-- Filtros de Checkbox com Dropdown -->
    <div class="row mt-3">
      <!-- Filtro de CFOP -->
      <div class="col-md-3">
        <div class="filter-dropdown-container">
          <div class="filter-dropdown-header" onclick="toggleFilterDropdown('cfop')">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">CFOP</h5>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2" id="cfop-count">0</span>
                <i class="fas fa-chevron-down filter-dropdown-icon" id="cfop-icon"></i>
              </div>
            </div>
          </div>
          <div class="filter-dropdown-content" id="cfop-dropdown">
            <div class="filter-options" id="cfop-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de NCM -->
      <div class="col-md-3">
        <div class="filter-dropdown-container">
          <div class="filter-dropdown-header" onclick="toggleFilterDropdown('ncm')">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">NCM</h5>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2" id="ncm-count">0</span>
                <i class="fas fa-chevron-down filter-dropdown-icon" id="ncm-icon"></i>
              </div>
            </div>
          </div>
          <div class="filter-dropdown-content" id="ncm-dropdown">
            <div class="filter-options" id="ncm-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de CST -->
      <div class="col-md-3">
        <div class="filter-dropdown-container">
          <div class="filter-dropdown-header" onclick="toggleFilterDropdown('cst')">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">CST</h5>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2" id="cst-count">0</span>
                <i class="fas fa-chevron-down filter-dropdown-icon" id="cst-icon"></i>
              </div>
            </div>
          </div>
          <div class="filter-dropdown-content" id="cst-dropdown">
            <div class="filter-options" id="cst-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de Alíquota -->
      <div class="col-md-3">
        <div class="filter-dropdown-container">
          <div class="filter-dropdown-header" onclick="toggleFilterDropdown('aliquota')">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">Alíquota (%)</h5>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2" id="aliquota-count">0</span>
                <i class="fas fa-chevron-down filter-dropdown-icon" id="aliquota-icon"></i>
              </div>
            </div>
          </div>
          <div class="filter-dropdown-content" id="aliquota-dropdown">
            <div class="filter-options" id="aliquota-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botões de ação para filtros -->
    <div class="row mt-3">
      <div class="col-12 text-center">
        <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="limparTodosFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button type="button" class="btn btn-outline-info btn-sm" onclick="mostrarResumoFiltros()">
          <i class="fas fa-info-circle"></i> Resumo dos Filtros
        </button>
      </div>
    </div>
  `;

  container.appendChild(filtrosContainer);

  // Criar tabela
  const tableContainer = document.createElement('div');
  tableContainer.className = 'table-responsive';

  const table = document.createElement('table');
  table.className = 'table table-striped table-bordered';
  table.id = 'audit-dashboard-detail-table';
  table.innerHTML = `
    <thead>
      <tr>
        <th style="width: 40px;"></th>
        <th>Origem</th>
        <th>Número</th>
        <th>CFOP</th>
        <th>Produto</th>
        <th>Descrição</th>
        <th>NCM</th>
        <th>CEST</th>
        <th>CST</th>
        <th>Base de Cálculo</th>
        <th>Alíquota (%)</th>
        <th>Valor</th>
        <th>Status</th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody id="audit-dashboard-detail-tbody">
    </tbody>
  `;

  tableContainer.appendChild(table);
  container.appendChild(tableContainer);

  // Preencher tabela
  preencherTabelaDetalhamento(resultados);

  // Configurar filtros
  configurarFiltrosDetalhamento(tipoTributo);
}

/**
 * Preenche a tabela de detalhamento com interface de expansão
 * @param {Array} resultados - Dados dos resultados
 */
function preencherTabelaDetalhamento(resultados) {
  const tbody = document.getElementById('audit-dashboard-detail-tbody');
  tbody.innerHTML = '';

  if (resultados.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="14" class="text-center">Nenhum resultado encontrado</td>
      </tr>
    `;
    return;
  }

  // Filtrar apenas resultados inconsistentes
  const resultadosInconsistentes = resultados.filter(
    (r) => r.status === 'inconsistente',
  );

  if (resultadosInconsistentes.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="14" class="text-center">Nenhum resultado inconsistente encontrado</td>
      </tr>
    `;
    return;
  }

  resultadosInconsistentes.forEach((resultado) => {
    const notaId = `nota-${resultado.id}`;
    const cenarioId = `cenario-${resultado.id}`;

    // Linha da nota (sempre visível)
    const trNota = document.createElement('tr');
    trNota.className = 'nota-inconsistente-row';
    trNota.id = notaId;

    const aliquotaNotaFormatada = resultado.aliquota
      ? `${parseFloat(resultado.aliquota).toFixed(2)}%`
      : '-';

    trNota.innerHTML = `
      <td>
        <button class="expand-scenario-btn" onclick="toggleScenario('${cenarioId}', this)" title="Mostrar/Ocultar Cenário">
          <i class="fas fa-chevron-right"></i>
        </button>
      </td>
      <td><span class="badge bg-danger">NFe</span></td>
      <td>${resultado.numero || '-'}</td>
      <td>${resultado.cfop || '-'}</td>
      <td>${resultado.produto_numero || '-'}</td>
      <td>${resultado.produto_descricao || '-'}</td>
      <td>${resultado.ncm || '-'}</td>
      <td>${resultado.cest || '-'}</td>
      <td>${resultado.cst || '-'}</td>
      <td class="${
        resultado.comparacao?.base_calculo_diferente ? 'text-danger' : ''
      } text-end">${
      resultado.base_calculo ? formatCurrency(resultado.base_calculo) : '-'
    }</td>
      <td class="${
        resultado.comparacao?.aliquota_diferente ? 'text-danger' : ''
      } text-end">${aliquotaNotaFormatada}</td>
      <td class="${
        resultado.comparacao?.valor_diferente ? 'text-danger' : ''
      } text-end">${formatCurrency(resultado.valor)}</td>
      <td><span class="badge bg-danger">Inconsistente</span></td>
      <td>
        <div class="action-buttons">
          <button class="btn btn-view" onclick="visualizarDetalhes('nota', ${
            resultado.id
          })" title="Visualizar detalhes">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-report" onclick="gerarRelatorio(${
            resultado.id
          }, this)" title="Gerar relatório PDF">
            <i class="fas fa-file-pdf"></i>
          </button>
          ${
            resultado.analista_visualizou
              ? `<button class="btn btn-success btn-sm" title="Inconsistência analisada em ${
                  resultado.data_visualizacao
                    ? new Date(resultado.data_visualizacao).toLocaleDateString(
                        'pt-BR',
                      )
                    : 'N/A'
                }">
              <i class="fas fa-check"></i>
            </button>`
              : `<button class="btn btn-warning" onclick="marcarComoVista(${resultado.id})" title="Marcar como analisada">
              <i class="fas fa-eye-slash"></i>
            </button>`
          }
        </div>
      </td>
    `;

    tbody.appendChild(trNota);

    // Linha do cenário (inicialmente oculta)
    const trCenario = document.createElement('tr');
    trCenario.className = 'scenario-row';
    trCenario.id = cenarioId;

    const aliquotaCenarioFormatada = resultado.cenario.aliquota
      ? `${parseFloat(resultado.cenario.aliquota).toFixed(2)}%`
      : '-';

    trCenario.innerHTML = `
      <td></td>
      <td><span class="badge bg-success">Cenário</span></td>
      <td>${resultado.cenario.id || '-'}</td>
      <td>${resultado.cfop || '-'}</td>
      <td>${resultado.produto_numero || '-'}</td>
      <td>${resultado.produto_descricao || '-'}</td>
      <td>${resultado.ncm || '-'}</td>
      <td>${resultado.cest || '-'}</td>
      <td>${resultado.cenario.cst || '-'}</td>
      <td class="${
        resultado.comparacao?.base_calculo_diferente ? 'text-success' : ''
      } text-end">${
      resultado.cenario.base_calculo
        ? formatCurrency(resultado.cenario.base_calculo)
        : '-'
    }</td>
      <td class="${
        resultado.comparacao?.aliquota_diferente ? 'text-success' : ''
      } text-end">${aliquotaCenarioFormatada}</td>
      <td class="${
        resultado.comparacao?.valor_diferente ? 'text-success' : ''
      } text-end">${formatCurrency(resultado.cenario.valor)}</td>
      <td><span class="badge bg-success">Cenário</span></td>
      <td>
        <div class="action-buttons">
          <button class="btn btn-view" onclick="visualizarDetalhes('cenario', ${
            resultado.cenario.id || resultado.id
          })" title="Visualizar detalhes">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </td>
    `;

    tbody.appendChild(trCenario);
  });
}

/**
 * Alterna a visibilidade do cenário
 * @param {string} cenarioId - ID da linha do cenário
 * @param {HTMLElement} button - Botão de expansão
 */
function toggleScenario(cenarioId, button) {
  const cenarioRow = document.getElementById(cenarioId);
  const icon = button.querySelector('i');

  if (cenarioRow.classList.contains('show')) {
    // Ocultar cenário
    cenarioRow.classList.remove('show');
    icon.classList.remove('fa-chevron-down');
    icon.classList.add('fa-chevron-right');
    button.classList.remove('expanded');
  } else {
    // Mostrar cenário
    cenarioRow.classList.add('show');
    icon.classList.remove('fa-chevron-right');
    icon.classList.add('fa-chevron-down');
    button.classList.add('expanded');
  }
}

/**
 * Função de debounce para otimizar filtros
 * @param {Function} func - Função a ser executada
 * @param {number} delay - Delay em milissegundos
 * @param {string} key - Chave única para o timer
 */
function debounce(func, delay, key) {
  // Limpar timer anterior se existir
  if (debounceTimers.has(key)) {
    clearTimeout(debounceTimers.get(key));
  }

  // Criar novo timer
  const timer = setTimeout(func, delay);
  debounceTimers.set(key, timer);
}

/**
 * Configura os filtros da tabela de detalhamento
 * @param {string} tipoTributo - Tipo de tributo
 */
function configurarFiltrosDetalhamento(tipoTributo) {
  // Implementar filtros em tempo real com debounce
  const filtros = [
    'analista-visualizou',
    'atividade',
    'destinacao',
    'numero',
    'cfop',
    'produto',
    'ncm',
    'cst',
  ];

  filtros.forEach((filtro) => {
    const elemento = document.getElementById(`filtro-${filtro}`);
    if (elemento) {
      // Para selects, aplicar filtro imediatamente
      if (elemento.tagName === 'SELECT') {
        elemento.addEventListener('change', () =>
          aplicarFiltrosDetalhamento(tipoTributo),
        );
      } else {
        // Para inputs, usar debounce de 500ms
        elemento.addEventListener('input', () => {
          debounce(
            () => aplicarFiltrosDetalhamento(tipoTributo),
            500,
            `filtro-${filtro}`,
          );
        });
      }
    }
  });

  // Carregar opções para os filtros de checkbox
  carregarOpcoesFiltros(tipoTributo);

  // Adicionar evento para fechar dropdowns ao clicar fora
  document.addEventListener('click', (event) => {
    const isDropdownClick = event.target.closest('.filter-dropdown-container');
    if (!isDropdownClick) {
      fecharTodosDropdowns();
    }
  });
}

/**
 * Obtém os valores selecionados dos checkboxes de filtro
 * @param {string} type - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @returns {Array} Array com os valores selecionados
 */
function getValoresFiltroSelecionados(type) {
  const checkboxes = document.querySelectorAll(
    `.filtro-checkbox[data-type="${type}"]:checked`,
  );
  return Array.from(checkboxes).map((cb) => cb.value);
}

/**
 * Aplica os filtros na tabela de detalhamento
 * @param {string} tipoTributo - Tipo de tributo
 */
function aplicarFiltrosDetalhamento(tipoTributo) {
  // Obter valores dos filtros
  const filtros = {
    analista_visualizou:
      document.getElementById('filtro-analista-visualizou')?.value || '',
    atividade: document.getElementById('filtro-atividade')?.value || '',
    destinacao: document.getElementById('filtro-destinacao')?.value || '',
    numero: document.getElementById('filtro-numero')?.value || '',
    cfop: getValoresFiltroSelecionados('cfop'),
    produto: document.getElementById('filtro-produto')?.value || '',
    ncm: getValoresFiltroSelecionados('ncm'),
    cst: getValoresFiltroSelecionados('cst'),
    aliquota: getValoresFiltroSelecionados('aliquota'),
  };

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  // Criar chave de cache
  const cacheKey = `${empresaId}-${tipoTributo}-${year}-${month}-${JSON.stringify(
    filtros,
  )}`;

  // Verificar se existe no cache
  if (detalhamentoCache.has(cacheKey)) {
    console.log('Usando dados do cache para filtros');
    preencherTabelaDetalhamento(detalhamentoCache.get(cacheKey));
    return;
  }

  // Construir URL com filtros - sempre filtrar apenas inconsistências
  let url = `http://127.0.0.1:5000/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}&status=inconsistente`;

  // Adicionar filtros à URL
  Object.keys(filtros).forEach((key) => {
    const value = filtros[key];
    if (value) {
      if (Array.isArray(value) && value.length > 0) {
        // Para arrays, adicionar cada valor como parâmetro repetido
        value.forEach((v) => {
          if (v && v !== 'todos' && String(v).trim() !== '') {
            url += `&${key}=${encodeURIComponent(v)}`;
          }
        });
      } else if (value !== 'todos' && String(value).trim() !== '') {
        // Para valores únicos
        url += `&${key}=${encodeURIComponent(value)}`;
      }
    }
  });

  // Buscar dados filtrados
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Armazenar no cache
        detalhamentoCache.set(cacheKey, data.resultados);
        preencherTabelaDetalhamento(data.resultados);
      } else {
        console.error('Erro na resposta da API:', data.message);
      }
    })
    .catch((error) => {
      console.error('Erro ao aplicar filtros:', error);
    });
}

/**
 * Carrega as opções para os filtros de checkbox
 * @param {string} tipoTributo - Tipo de tributo
 */
function carregarOpcoesFiltros(tipoTributo) {
  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) return;

  // Construir URL para buscar opções de filtro
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard/filtros?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}`;

  // Buscar opções de filtro
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        const opcoes = data.opcoes;

        // Armazenar relacionamentos globalmente para uso posterior
        window.filtroRelacionamentos = opcoes;

        // Preencher CFOPs com relacionamentos
        if (opcoes.cfops && opcoes.cfops.length > 0) {
          criarCheckboxFiltroComRelacionamentos('cfop', opcoes.cfops);
        }

        // Preencher NCMs com relacionamentos
        if (opcoes.ncms && opcoes.ncms.length > 0) {
          criarCheckboxFiltroComRelacionamentos('ncm', opcoes.ncms);
        }

        // Preencher CSTs com relacionamentos
        if (opcoes.csts && opcoes.csts.length > 0) {
          criarCheckboxFiltroComRelacionamentos('cst', opcoes.csts);
        }

        // Preencher Alíquotas com relacionamentos
        if (opcoes.aliquotas && opcoes.aliquotas.length > 0) {
          criarCheckboxFiltroComRelacionamentos('aliquota', opcoes.aliquotas);
        }
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar opções de filtro:', error);
    });
}

/**
 * Cria checkboxes para filtros com relacionamentos
 * @param {string} tipo - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @param {Array} opcoes - Array de opções com relacionamentos
 */
function criarCheckboxFiltroComRelacionamentos(tipo, opcoes) {
  const container = document.getElementById(`${tipo}-container`);
  if (!container) return;

  // Limpar container
  container.innerHTML = '';

  // Adicionar campo de busca
  const searchId = `busca-${tipo}`;
  const searchDiv = document.createElement('div');
  searchDiv.className = 'mb-2';
  searchDiv.innerHTML = `
    <input type="text"
           id="${searchId}"
           class="form-control form-control-sm"
           placeholder="Buscar ${tipo.toUpperCase()}...">
  `;
  container.appendChild(searchDiv);

  // Adicionar checkboxes
  const optionsDiv = document.createElement('div');
  optionsDiv.className = 'filter-options-list';
  optionsDiv.style.maxHeight = '200px';
  optionsDiv.style.overflowY = 'auto';

  // Ordenar opções
  let sortedOpcoes = [...opcoes];
  if (tipo === 'aliquota') {
    sortedOpcoes.sort((a, b) => parseFloat(a.value) - parseFloat(b.value));
  } else {
    sortedOpcoes.sort((a, b) => a.value.localeCompare(b.value));
  }

  sortedOpcoes.forEach((opcao) => {
    if (!opcao.value) return; // Ignorar valores vazios

    const checkboxId = `filtro-${tipo}-${opcao.value
      .toString()
      .replace(/[^a-zA-Z0-9]/g, '-')}`;
    const div = document.createElement('div');
    div.className = 'form-check';

    div.innerHTML = `
      <input class="form-check-input filtro-checkbox"
             type="checkbox"
             id="${checkboxId}"
             value="${opcao.value}"
             data-type="${tipo}"
             data-related='${JSON.stringify(opcao.related)}'>
      <label class="form-check-label" for="${checkboxId}">
        ${
          tipo === 'aliquota'
            ? `${parseFloat(opcao.value).toFixed(2)}%`
            : opcao.value
        }
      </label>
    `;
    optionsDiv.appendChild(div);
  });

  container.appendChild(optionsDiv);

  // Adicionar evento de busca
  const searchInput = document.getElementById(searchId);
  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase();
      const checkboxes = optionsDiv.querySelectorAll('.form-check');

      checkboxes.forEach((checkbox) => {
        const label = checkbox.querySelector('.form-check-label');
        if (label && label.textContent.toLowerCase().includes(searchTerm)) {
          checkbox.style.display = 'block';
        } else {
          checkbox.style.display = 'none';
        }
      });
    });
  }

  // Adicionar evento de mudança nos checkboxes
  const checkboxes = optionsDiv.querySelectorAll('.filtro-checkbox');
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener('change', () => {
      // Extrair tipo de tributo da URL
      const path = window.location.pathname;
      const tipoTributo = path.split('/').pop();

      // Atualizar contador
      atualizarContadorFiltro(tipo);

      // Aplicar filtros relacionados
      aplicarFiltrosRelacionados();

      // Aplicar filtros na tabela
      if (tipoTributo && tipoTributo !== 'auditoria') {
        aplicarFiltrosDetalhamento(tipoTributo);
      }
    });
  });

  // Inicializar contador
  atualizarContadorFiltro(tipo);
}

/**
 * Aplica filtros relacionados entre CFOP, NCM, CST e Alíquota
 */
function aplicarFiltrosRelacionados() {
  const cfopsSelecionados = getValoresFiltroSelecionados('cfop');
  const ncmsSelecionados = getValoresFiltroSelecionados('ncm');
  const cstsSelecionados = getValoresFiltroSelecionados('cst');
  const aliquotasSelecionadas = getValoresFiltroSelecionados('aliquota');

  // Se não há filtros selecionados, mostrar todos
  if (
    cfopsSelecionados.length === 0 &&
    ncmsSelecionados.length === 0 &&
    cstsSelecionados.length === 0 &&
    aliquotasSelecionadas.length === 0
  ) {
    mostrarTodosOsFiltros();
    return;
  }

  // Filtrar NCMs baseado nos CFOPs selecionados
  if (cfopsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecao('ncm', 'cfops', cfopsSelecionados);

    // Se há CFOPs selecionados mas nenhum NCM, filtrar CSTs e alíquotas baseado nos CFOPs
    if (ncmsSelecionados.length === 0) {
      filtrarOpcoesBaseadoEmSelecao('cst', 'cfops', cfopsSelecionados);
      filtrarOpcoesBaseadoEmSelecao('aliquota', 'cfops', cfopsSelecionados);
    }
  }

  // Filtrar CSTs baseado nos NCMs selecionados
  if (ncmsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecao('cst', 'ncms', ncmsSelecionados);

    // Se há NCMs selecionados mas nenhum CST, filtrar alíquotas baseado nos NCMs
    if (cstsSelecionados.length === 0) {
      filtrarOpcoesBaseadoEmSelecao('aliquota', 'ncms', ncmsSelecionados);
    }
  }

  // Filtrar Alíquotas baseado nos CSTs selecionados
  if (cstsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecao('aliquota', 'csts', cstsSelecionados);
  }
}

/**
 * Filtra opções baseado em uma seleção de valores
 * @param {string} targetType - Tipo de opção a ser filtrada
 * @param {string} relatedKey - Chave do relacionamento
 * @param {Array} selectedValues - Valores selecionados
 */
function filtrarOpcoesBaseadoEmSelecao(targetType, relatedKey, selectedValues) {
  const checkboxes = document.querySelectorAll(
    `.filtro-checkbox[data-type="${targetType}"]`,
  );

  checkboxes.forEach((checkbox) => {
    try {
      const related = JSON.parse(checkbox.dataset.related || '{}');
      const relatedValues = related[relatedKey] || [];

      // Verificar se algum dos valores selecionados está nos valores relacionados
      const hasMatch = selectedValues.some((value) =>
        relatedValues.includes(value),
      );

      const formCheck = checkbox.closest('.form-check');
      if (hasMatch) {
        formCheck.style.display = 'block';
      } else {
        formCheck.style.display = 'none';
        // Se estiver ocultando, desmarcar o checkbox
        if (checkbox.checked) {
          checkbox.checked = false;
          // Atualizar contador quando desmarcar
          atualizarContadorFiltro(targetType);
        }
      }
    } catch (e) {
      console.error(
        'Erro ao processar relacionamentos:',
        e,
        checkbox.dataset.related,
      );
    }
  });
}

/**
 * Mostra todos os filtros (remove filtros relacionados)
 */
function mostrarTodosOsFiltros() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    const checkboxes = document.querySelectorAll(
      `.filtro-checkbox[data-type="${tipo}"]`,
    );
    checkboxes.forEach((checkbox) => {
      checkbox.closest('.form-check').style.display = 'block';
    });
  });
}

/**
 * Limpa todos os filtros selecionados
 */
function limparTodosFiltros() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    const checkboxes = document.querySelectorAll(
      `.filtro-checkbox[data-type="${tipo}"]`,
    );
    checkboxes.forEach((checkbox) => {
      checkbox.checked = false;
      checkbox.closest('.form-check').style.display = 'block';
    });

    // Atualizar contador
    atualizarContadorFiltro(tipo);
  });

  // Aplicar filtros (que agora estarão vazios)
  // Extrair tipo de tributo da URL
  const path = window.location.pathname;
  const tipoTributo = path.split('/').pop();

  if (tipoTributo && tipoTributo !== 'auditoria') {
    aplicarFiltrosDetalhamento(tipoTributo);
  }
}

/**
 * Mostra resumo dos filtros ativos
 */
function mostrarResumoFiltros() {
  const resumo = {
    cfop: getValoresFiltroSelecionados('cfop'),
    ncm: getValoresFiltroSelecionados('ncm'),
    cst: getValoresFiltroSelecionados('cst'),
    aliquota: getValoresFiltroSelecionados('aliquota'),
  };

  let mensagem = 'Filtros Ativos:\n\n';
  let temFiltros = false;

  Object.keys(resumo).forEach((tipo) => {
    if (resumo[tipo].length > 0) {
      mensagem += `${tipo.toUpperCase()}: ${resumo[tipo].join(', ')}\n`;
      temFiltros = true;
    }
  });

  if (!temFiltros) {
    mensagem += 'Nenhum filtro ativo.';
  }

  alert(mensagem);
}

/**
 * Atualiza o contador de filtros selecionados
 * @param {string} tipo - Tipo de filtro
 */
function atualizarContadorFiltro(tipo) {
  const selecionados = getValoresFiltroSelecionados(tipo);
  const contador = document.getElementById(`${tipo}-count`);
  if (contador) {
    contador.textContent = selecionados.length;
    contador.className =
      selecionados.length > 0
        ? 'badge bg-success me-2'
        : 'badge bg-primary me-2';
  }
}

/**
 * Alterna a visibilidade do dropdown de filtro
 * @param {string} tipo - Tipo de filtro (cfop, ncm, cst, aliquota)
 */
function toggleFilterDropdown(tipo) {
  const dropdown = document.getElementById(`${tipo}-dropdown`);
  const icon = document.getElementById(`${tipo}-icon`);

  if (!dropdown || !icon) return;

  // Fechar outros dropdowns
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((t) => {
    if (t !== tipo) {
      const otherDropdown = document.getElementById(`${t}-dropdown`);
      const otherIcon = document.getElementById(`${t}-icon`);
      if (otherDropdown && otherIcon) {
        otherDropdown.classList.remove('show');
        otherIcon.classList.remove('fa-chevron-up');
        otherIcon.classList.add('fa-chevron-down');
      }
    }
  });

  // Alternar o dropdown atual
  if (dropdown.classList.contains('show')) {
    dropdown.classList.remove('show');
    icon.classList.remove('fa-chevron-up');
    icon.classList.add('fa-chevron-down');
  } else {
    dropdown.classList.add('show');
    icon.classList.remove('fa-chevron-down');
    icon.classList.add('fa-chevron-up');
  }
}

/**
 * Fecha todos os dropdowns de filtro
 */
function fecharTodosDropdowns() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    const dropdown = document.getElementById(`${tipo}-dropdown`);
    const icon = document.getElementById(`${tipo}-icon`);
    if (dropdown && icon) {
      dropdown.classList.remove('show');
      icon.classList.remove('fa-chevron-up');
      icon.classList.add('fa-chevron-down');
    }
  });
}

/**
 * Filtra opções relacionadas com base em um filtro selecionado
 * @param {string} targetType - Tipo de opção a ser filtrada (ex: 'ncm')
 * @param {string} filterType - Tipo do filtro selecionado (ex: 'cfop')
 * @param {string} filterValue - Valor do filtro selecionado
 */
function filtrarOpcoesRelacionadas(targetType, filterType, filterValue) {
  const checkboxes = document.querySelectorAll(
    `.filtro-checkbox[data-type="${targetType}"]`,
  );

  checkboxes.forEach((checkbox) => {
    const relatedValue = checkbox.dataset[filterType];
    // Se o checkbox tem um valor relacionado ao filtro, mostrar/ocultar com base na correspondência
    if (relatedValue) {
      const shouldShow = relatedValue === filterValue;
      checkbox.closest('.form-check').style.display = shouldShow
        ? 'block'
        : 'none';
    }
  });
}

/**
 * Cria os checkboxes para um tipo de filtro
 * @param {string} tipo - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @param {Array} valores - Valores para os checkboxes
 */
function criarCheckboxFiltro(tipo, valores) {
  const container = document.getElementById(`${tipo}-container`);
  if (!container) return;

  // Ordenar valores (exceto para alíquotas que devem manter a ordem numérica)
  if (tipo !== 'aliquota') {
    valores = [...new Set(valores)].sort();
  } else {
    // Para alíquotas, converter para número, ordenar e depois voltar para string
    valores = [...new Set(valores)]
      .map((v) => parseFloat(v))
      .sort((a, b) => a - b)
      .map((v) => v.toString());
  }

  // Limpar container
  container.innerHTML = '';

  // Adicionar campo de busca
  const searchId = `busca-${tipo}`;
  const searchDiv = document.createElement('div');
  searchDiv.className = 'mb-2';
  searchDiv.innerHTML = `
    <input type="text"
           id="${searchId}"
           class="form-control form-control-sm"
           placeholder="Buscar ${tipo.toUpperCase()}...">
  `;
  container.appendChild(searchDiv);

  // Adicionar checkboxes
  const optionsDiv = document.createElement('div');
  optionsDiv.className = 'filter-options-list';
  optionsDiv.style.maxHeight = '200px';
  optionsDiv.style.overflowY = 'auto';

  valores.forEach((valor) => {
    if (!valor) return; // Ignorar valores vazios

    const checkboxId = `filtro-${tipo}-${valor
      .toString()
      .replace(/[^a-zA-Z0-9]/g, '-')}`;
    const div = document.createElement('div');
    div.className = 'form-check';
    div.innerHTML = `
      <input class="form-check-input filtro-checkbox"
             type="checkbox"
             id="${checkboxId}"
             value="${valor}"
             data-type="${tipo}">
      <label class="form-check-label" for="${checkboxId}">
        ${tipo === 'aliquota' ? `${parseFloat(valor).toFixed(2)}%` : valor}
      </label>
    `;
    optionsDiv.appendChild(div);
  });

  container.appendChild(optionsDiv);

  // Adicionar evento de busca
  const searchInput = document.getElementById(searchId);
  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase();
      const checkboxes = optionsDiv.querySelectorAll('.form-check');

      checkboxes.forEach((checkbox) => {
        const label = checkbox.querySelector('.form-check-label');
        if (label && label.textContent.toLowerCase().includes(searchTerm)) {
          checkbox.style.display = 'block';
        } else {
          checkbox.style.display = 'none';
        }
      });
    });
  }

  // Adicionar evento de mudança nos checkboxes
  const checkboxes = optionsDiv.querySelectorAll('.filtro-checkbox');
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener('change', () => {
      // Extrair tipo de tributo da URL
      const path = window.location.pathname;
      const tipoTributo = path.split('/').pop();

      // Se for um CFOP, filtrar NCMs relacionados
      if (tipo === 'cfop' && checkbox.checked) {
        // Desmarcar outros CFOPs (seletor único)
        checkboxes.forEach((cb) => {
          if (cb !== checkbox) cb.checked = false;
        });

        // Filtrar NCMs relacionados
        filtrarOpcoesRelacionadas('ncm', 'cfop', checkbox.value);

        // Limpar seleção de NCMs
        document
          .querySelectorAll('.filtro-checkbox[data-type="ncm"]')
          .forEach((cb) => {
            cb.checked = false;
          });
      }

      // Se for um NCM, verificar se há um CFOP selecionado
      if (tipo === 'ncm' && checkbox.checked) {
        const cfopSelecionado = document.querySelector(
          '.filtro-checkbox[data-type="cfop"]:checked',
        );
        if (!cfopSelecionado) {
          checkbox.checked = false;
          alert('Selecione um CFOP antes de selecionar um NCM.');
          return;
        }
      }

      if (tipoTributo && tipoTributo !== 'auditoria') {
        aplicarFiltrosDetalhamento(tipoTributo);
      }
    });
  });
}

/**
 * Carrega as opções para autocomplete dos filtros
 * @param {string} tipoTributo - Tipo de tributo
 */
function carregarOpcoesAutocomplete(tipoTributo) {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  // Criar chave de cache para opções
  const cacheKey = `opcoes-${empresaId}-${tipoTributo}-${year}-${month}`;

  // Verificar se existe no cache
  if (filtroOptionsCache.has(cacheKey)) {
    console.log('Usando opções do cache para autocomplete');
    preencherOpcoesAutocomplete(filtroOptionsCache.get(cacheKey));
    return;
  }

  // Buscar opções da API - apenas inconsistências
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}&status=inconsistente`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Extrair opções únicas dos resultados
        const opcoes = extrairOpcoesUnicas(data.resultados);

        // Armazenar no cache
        filtroOptionsCache.set(cacheKey, opcoes);

        // Preencher autocomplete
        preencherOpcoesAutocomplete(opcoes);
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar opções de autocomplete:', error);
    });
}

/**
 * Extrai opções únicas dos resultados para autocomplete
 * @param {Array} resultados - Array de resultados de auditoria
 */
function extrairOpcoesUnicas(resultados) {
  const cfops = new Set();
  const ncms = new Set();
  const csts = new Set();
  const aliquotas = new Set();

  resultados.forEach((resultado) => {
    if (resultado.cfop)
      cfops.add(JSON.stringify({ value: resultado.cfop, ncm: resultado.ncm }));
    if (resultado.ncm)
      ncms.add(JSON.stringify({ value: resultado.ncm, cfop: resultado.cfop }));
    if (resultado.cst) csts.add(resultado.cst);
    if (resultado.aliquota) aliquotas.add(resultado.aliquota);
  });

  return {
    cfops: Array.from(cfops).map(JSON.parse),
    ncms: Array.from(ncms).map(JSON.parse),
    csts: Array.from(csts).sort(),
    aliquotas: Array.from(aliquotas).sort((a, b) => a - b),
  };
}

/**
 * Cria um elemento de checkbox para filtro
 * @param {string} type - Tipo do filtro (cfop, ncm, cst, aliquota)
 * @param {string} value - Valor do filtro
 * @param {string} label - Texto a ser exibido
 * @param {string} parentId - ID do container pai
 * @param {Object} [relatedValues] - Valores relacionados (ex: {ncm: '1234'} para CFOP)
 */
function criarCheckboxFiltro(type, value, label, parentId, relatedValues = {}) {
  const container = document.createElement('div');
  container.className = 'form-check';

  const id = `filtro-${type}-${value.replace(/[^a-zA-Z0-9]/g, '-')}`;

  const input = document.createElement('input');
  input.className = 'form-check-input filtro-checkbox';
  input.type = 'checkbox';
  input.id = id;
  input.value = value;
  input.dataset.type = type;

  // Adiciona dados relacionados como atributos data
  Object.entries(relatedValues).forEach(([key, val]) => {
    if (val) input.dataset[key] = val;
  });

  const labelEl = document.createElement('label');
  labelEl.className = 'form-check-label';
  labelEl.htmlFor = id;
  labelEl.textContent = label || value;

  container.appendChild(input);
  container.appendChild(labelEl);

  // Adiciona evento para atualizar filtros quando o checkbox for alterado
  input.addEventListener('change', () => {
    const tipoTributo =
      document.querySelector('.nav-pills .active')?.dataset.tributo;
    if (tipoTributo) aplicarFiltrosDetalhamento(tipoTributo);

    // Se for um CFOP, filtrar NCMs relacionados
    if (type === 'cfop' && input.checked) {
      filtrarOpcoesRelacionadas('ncm', 'cfop', value);
    }
  });

  document.getElementById(parentId).appendChild(container);
  return input;
}

/**
 * Filtra opções relacionadas com base em um filtro selecionado
 * @param {string} targetType - Tipo de opção a ser filtrada (ex: 'ncm')
 * @param {string} filterType - Tipo do filtro selecionado (ex: 'cfop')
 * @param {string} filterValue - Valor do filtro selecionado
 */

checkboxes.forEach((checkbox) => {
  const relatedValue = checkbox.dataset[filterType];
  // Se o checkbox tem um valor relacionado ao filtro, mostrar/ocultar com base na correspondência
  if (relatedValue) {
    const shouldShow = relatedValue === filterValue;
    checkbox.closest('.form-check').style.display = shouldShow
      ? 'block'
      : 'none';
  }
});

/**
 * Preenche as opções de filtro como checkboxes
 * @param {Object} opcoes - Objeto com arrays de opções
 */
function preencherOpcoesAutocomplete(opcoes) {
  // Limpar containers existentes
  [
    'cfop-container',
    'ncm-container',
    'cst-container',
    'aliquota-container',
  ].forEach((id) => {
    const container = document.getElementById(id);
    if (container) container.innerHTML = '';
  });

  // Preencher CFOPs
  const uniqueCfops = [];
  opcoes.cfops.forEach((cfop) => {
    if (!uniqueCfops.includes(cfop.value)) {
      uniqueCfops.push(cfop.value);
      criarCheckboxFiltro(
        'cfop',
        cfop.value,
        `CFOP ${cfop.value}`,
        'cfop-container',
        { ncm: cfop.ncm },
      );
    }
  });

  // Preencher NCMs
  const uniqueNcms = [];
  opcoes.ncms.forEach((ncm) => {
    if (!uniqueNcms.includes(ncm.value)) {
      uniqueNcms.push(ncm.value);
      criarCheckboxFiltro(
        'ncm',
        ncm.value,
        `NCM ${ncm.value}`,
        'ncm-container',
        { cfop: ncm.cfop },
      );
    }
  });

  // Preencher CSTs
  opcoes.csts.forEach((cst) => {
    criarCheckboxFiltro('cst', cst, `CST ${cst}`, 'cst-container');
  });

  // Preencher Alíquotas
  opcoes.aliquotas.forEach((aliquota) => {
    criarCheckboxFiltro(
      'aliquota',
      aliquota,
      `${aliquota}%`,
      'aliquota-container',
    );
  });
}

/**
 * Visualiza detalhes de uma nota ou cenário
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {number} id - ID do registro
 */
function visualizarDetalhes(tipo, id) {
  // Buscar dados detalhados
  const empresaId = localStorage.getItem('selectedCompany');
  const url = `http://127.0.0.1:5000/api/auditoria/detalhes/${tipo}/${id}?empresa_id=${empresaId}`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        mostrarModalDetalhes(tipo, data.detalhes);
      } else {
        alert(`Erro ao carregar detalhes: ${data.message}`);
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar detalhes:', error);
      alert(`Erro ao carregar detalhes: ${error.message}`);
    });
}

/**
 * Mostra modal com detalhes da nota ou cenário
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {Object} detalhes - Dados detalhados
 */
function mostrarModalDetalhes(tipo, detalhes) {
  const modalId = 'modal-detalhes-auditoria';

  // Remover modal existente se houver
  const existingModal = document.getElementById(modalId);
  if (existingModal) {
    existingModal.remove();
  }

  // Criar modal
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = modalId;
  modal.setAttribute('tabindex', '-1');
  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas ${
              tipo === 'nota' ? 'fa-file-invoice' : 'fa-cogs'
            }"></i>
            Detalhes ${tipo === 'nota' ? 'da Nota Fiscal' : 'do Cenário'}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          ${gerarConteudoModal(tipo, detalhes)}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Mostrar modal
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // Remover modal do DOM quando fechado
  modal.addEventListener('hidden.bs.modal', () => {
    modal.remove();
  });
}

/**
 * Gera conteúdo do modal baseado no tipo
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {Object} detalhes - Dados detalhados
 */
function gerarConteudoModal(tipo, detalhes) {
  if (tipo === 'nota') {
    return `
      <div class="row">
        <div class="col-md-6">
          <h6><i class="fas fa-building"></i> Dados do Cliente</h6>
          <table class="table table-sm">
            <tr><td><strong>Razão Social:</strong></td><td>${
              detalhes.cliente?.razao_social || '-'
            }</td></tr>
            <tr><td><strong>CNPJ:</strong></td><td>${
              detalhes.cliente?.cnpj || '-'
            }</td></tr>
            <tr><td><strong>Inscrição Estadual:</strong></td><td>${
              detalhes.cliente?.inscricao_estadual || '-'
            }</td></tr>
            <tr><td><strong>Município:</strong></td><td>${
              detalhes.cliente?.municipio || '-'
            }</td></tr>
            <tr><td><strong>UF:</strong></td><td>${
              detalhes.cliente?.uf || '-'
            }</td></tr>
            <tr><td><strong>Atividade:</strong></td><td>${
              detalhes.cliente?.atividade || '-'
            }</td></tr>
            <tr><td><strong>Destinação:</strong></td><td>${
              detalhes.cliente?.destinacao || '-'
            }</td></tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6><i class="fas fa-box"></i> Dados do Produto</h6>
          <table class="table table-sm">
            <tr><td><strong>Código:</strong></td><td>${
              detalhes.produto?.codigo || '-'
            }</td></tr>
            <tr><td><strong>Descrição:</strong></td><td>${
              detalhes.produto?.descricao || '-'
            }</td></tr>
            <tr><td><strong>NCM:</strong></td><td>${
              detalhes.nota_fiscal_item?.ncm || '-'
            }</td></tr>
            <tr><td><strong>CEST:</strong></td><td>${
              detalhes.produto?.cest || '-'
            }</td></tr>
            <tr><td><strong>CFOP:</strong></td><td>${
              detalhes.nota_fiscal_item?.cfop || '-'
            }</td></tr>
          </table>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <h6><i class="fas fa-calculator"></i> Dados do Tributo na Nota</h6>
          <table class="table table-sm">
            <tr><td><strong>CST:</strong></td><td>${
              detalhes.tributo?.cst || '-'
            }</td></tr>
            <tr><td><strong>Base de Cálculo:</strong></td><td>${
              detalhes.tributo?.base_calculo
                ? formatCurrency(detalhes.tributo.base_calculo)
                : '-'
            }</td></tr>
            <tr><td><strong>Alíquota:</strong></td><td>${
              detalhes.tributo?.aliquota
                ? parseFloat(detalhes.tributo.aliquota).toFixed(2) + '%'
                : '-'
            }</td></tr>
            <tr><td><strong>Valor:</strong></td><td>${
              detalhes.tributo?.valor
                ? formatCurrency(detalhes.tributo.valor)
                : '-'
            }</td></tr>
            <tr><td><strong>Data Emissão:</strong></td><td>${
              detalhes.tributo?.data_emissao
                ? new Date(detalhes.tributo.data_emissao).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
          </table>
        </div>
      </div>
    `;
  } else {
    return `
      <div class="row">
        <div class="col-md-6">
          <h6><i class="fas fa-building"></i> Dados do Cliente</h6>
          <table class="table table-sm">
            <tr><td><strong>Razão Social:</strong></td><td>${
              detalhes.cliente?.razao_social || '-'
            }</td></tr>
            <tr><td><strong>CNPJ:</strong></td><td>${
              detalhes.cliente?.cnpj || '-'
            }</td></tr>
            <tr><td><strong>Inscrição Estadual:</strong></td><td>${
              detalhes.cliente?.inscricao_estadual || '-'
            }</td></tr>
            <tr><td><strong>Município:</strong></td><td>${
              detalhes.cliente?.municipio || '-'
            }</td></tr>
            <tr><td><strong>UF:</strong></td><td>${
              detalhes.cliente?.uf || '-'
            }</td></tr>
            <tr><td><strong>Atividade:</strong></td><td>${
              detalhes.cliente?.atividade || '-'
            }</td></tr>
            <tr><td><strong>Destinação:</strong></td><td>${
              detalhes.cliente?.destinacao || '-'
            }</td></tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6><i class="fas fa-box"></i> Dados do Produto</h6>
          <table class="table table-sm">
            <tr><td><strong>Código:</strong></td><td>${
              detalhes.produto?.codigo || '-'
            }</td></tr>
            <tr><td><strong>Descrição:</strong></td><td>${
              detalhes.produto?.descricao || '-'
            }</td></tr>
            <tr><td><strong>NCM:</strong></td><td>${
              detalhes.cenario?.ncm || '-'
            }</td></tr>
            <tr><td><strong>CEST:</strong></td><td>${
              detalhes.produto?.cest || '-'
            }</td></tr>
            <tr><td><strong>CFOP:</strong></td><td>${
              detalhes.cenario?.cfop || '-'
            }</td></tr>
          </table>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <h6><i class="fas fa-cogs"></i> Dados do Cenário</h6>
          <table class="table table-sm">
            <tr><td><strong>ID:</strong></td><td>${
              detalhes.cenario?.id || '-'
            }</td></tr>
            <tr><td><strong>Status:</strong></td><td>${
              detalhes.cenario?.status || '-'
            }</td></tr>
            <tr><td><strong>CST:</strong></td><td>${
              detalhes.cenario?.cst || '-'
            }</td></tr>
            <tr><td><strong>Alíquota:</strong></td><td>${
              detalhes.cenario?.aliquota
                ? parseFloat(detalhes.cenario.aliquota).toFixed(2) + '%'
                : '-'
            }</td></tr>
            <tr><td><strong>Validade Início:</strong></td><td>${
              detalhes.cenario?.validade_inicio
                ? new Date(detalhes.cenario.validade_inicio).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
            <tr><td><strong>Validade Fim:</strong></td><td>${
              detalhes.cenario?.validade_fim
                ? new Date(detalhes.cenario.validade_fim).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
          </table>
        </div>
      </div>
    `;
  }
}

/**
 * Gera relatório PDF para uma inconsistência
 * @param {number} id - ID do resultado de auditoria
 * @param {HTMLElement} btnElement - Elemento do botão clicado
 */
function gerarRelatorio(id, btnElement = null) {
  const empresaId = localStorage.getItem('selectedCompany');

  // Mostrar loading
  let btn = btnElement;
  if (!btn) {
    // Fallback para encontrar o botão se não foi passado
    btn = document.querySelector(`button[onclick*="gerarRelatorio(${id})"]`);
  }

  let originalContent = '';
  if (btn) {
    originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
  }

  const url = `http://127.0.0.1:5000/api/auditoria/relatorio/${id}?empresa_id=${empresaId}`;

  fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => {
      if (response.ok) {
        return response.blob();
      }
      throw new Error('Erro ao gerar relatório');
    })
    .then((blob) => {
      // Criar link para download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `relatorio_inconsistencia_${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    })
    .catch((error) => {
      console.error('Erro ao gerar relatório:', error);
      alert(`Erro ao gerar relatório: ${error.message}`);
    })
    .finally(() => {
      // Restaurar botão
      if (btn) {
        btn.innerHTML = originalContent;
        btn.disabled = false;
      }
    });
}

/**
 * Renderiza um dashboard vazio
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDashboardVazio(tipoTributo) {
  // Obter o container do dashboard
  const dashboardContainer = document.getElementById(
    'auditoria-dashboard-container',
  );
  if (!dashboardContainer) {
    console.error('Container do dashboard não encontrado');
    return;
  }

  // Limpar o container
  dashboardContainer.innerHTML = '';

  // Mensagem de dashboard vazio
  const emptyMessage = document.createElement('div');
  emptyMessage.className = 'alert alert-info text-center mt-4';
  emptyMessage.innerHTML = `
    <h4>Nenhum dado de auditoria encontrado</h4>
    <p>Não há dados de auditoria para o tributo ${tipoTributo.toUpperCase()} no período selecionado.</p>
    <p>Execute a auditoria para este tributo para visualizar o dashboard.</p>
    <button class="btn btn-primary mt-2" onclick="executarAuditoriaTributo('${tipoTributo}', window.location.pathname.includes('entrada') ? 'entrada' : 'saida')">
      <i class="fas fa-calculator"></i> Executar Auditoria
    </button>
  `;

  dashboardContainer.appendChild(emptyMessage);
}

/**
 * Marca uma inconsistência como vista pelo analista
 * @param {number} resultadoId - ID do resultado de auditoria
 */
function marcarComoVista(resultadoId) {
  // Criar modal para observações
  const modalId = 'modal-marcar-vista';

  // Remover modal existente se houver
  const existingModal = document.getElementById(modalId);
  if (existingModal) {
    existingModal.remove();
  }

  // Criar modal
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = modalId;
  modal.setAttribute('tabindex', '-1');
  modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-eye"></i>
            Marcar Inconsistência como Analisada
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="observacoes-analista" class="form-label">Observações do Analista</label>
            <textarea class="form-control" id="observacoes-analista" rows="4"
                      placeholder="Descreva as providências tomadas ou observações sobre esta inconsistência..."></textarea>
          </div>
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Ao marcar como analisada, esta inconsistência será identificada como vista pelo analista.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" onclick="confirmarMarcacaoVista(${resultadoId})">
            <i class="fas fa-check"></i> Marcar como Analisada
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Mostrar modal
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // Remover modal do DOM quando fechado
  modal.addEventListener('hidden.bs.modal', () => {
    modal.remove();
  });
}

/**
 * Confirma a marcação da inconsistência como vista
 * @param {number} resultadoId - ID do resultado de auditoria
 */
function confirmarMarcacaoVista(resultadoId) {
  const observacoes =
    document.getElementById('observacoes-analista')?.value || '';

  // Mostrar loading
  showLoading();

  const url = `http://127.0.0.1:5000/api/auditoria/marcar-vista/${resultadoId}`;

  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify({
      observacoes: observacoes,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success) {
        // Fechar modal
        const modal = document.getElementById('modal-marcar-vista');
        if (modal) {
          const bsModal = bootstrap.Modal.getInstance(modal);
          bsModal.hide();
        }

        // Mostrar mensagem de sucesso
        showSuccessMessage(
          'Inconsistência marcada como analisada com sucesso!',
        );

        // Limpar cache para forçar recarregamento
        limparCacheDetalhamento();

        // Recarregar a tabela de detalhamento
        const tipoTributo = window.location.pathname.split('/').pop();
        const detalhamentoTab = document.getElementById('detalhamento');
        if (detalhamentoTab) {
          detalhamentoTab.removeAttribute('data-loaded');
          carregarDetalhamentoAuditoria(tipoTributo, detalhamentoTab);
        }

        // Recarregar o dashboard para atualizar contadores
        carregarDashboardAuditoria(tipoTributo);
      } else {
        // Mostrar mensagem de erro
        const errorMsg =
          data.message || 'Erro ao marcar inconsistência como vista';
        console.error('Erro retornado pelo servidor:', errorMsg);
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      console.error('Erro ao marcar inconsistência como vista:', error);
      showErrorMessage(
        `Erro ao marcar inconsistência como vista: ${error.message}`,
      );
    });
}

/**
 * Limpa o cache de detalhamento para forçar recarregamento
 */
function limparCacheDetalhamento() {
  detalhamentoCache.clear();
  filtroOptionsCache.clear();
  console.log('Cache de detalhamento limpo');
}

/**
 * Executa a auditoria para um tipo de tributo específico
 * @param {string} tipoTributo - Tipo de tributo (icms, icms-st, ipi, pis, cofins, difal)
 * @param {string} tipoOperacao - Tipo de operação (entrada, saida)
 */
function executarAuditoriaTributo(tipoTributo, tipoOperacao) {
  // Mostrar loading
  showLoading();

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    hideLoading();
    showErrorMessage('Selecione uma empresa para executar a auditoria.');
    return;
  }

  // Construir URL para executar a auditoria
  const url = `http://127.0.0.1:5000/api/auditoria/executar`;

  // Preparar dados para a requisição
  const data = {
    empresa_id: parseInt(empresaId),
    tipo_tributo: tipoTributo,
    tipo_operacao: tipoOperacao === 'entrada' ? 0 : 1, // 0 = entrada, 1 = saída
    year: parseInt(year),
    month: parseInt(month),
    forcar_recalculo: true, // Sempre forçar recálculo para garantir valores corretos
  };

  // Executar a auditoria
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success && data.audit_id) {
        // Iniciar acompanhamento de progresso via WebSocket
        if (window.auditoriaProgressManager) {
          window.auditoriaProgressManager.startAuditProgress(
            data.audit_id,
            0, // Total será atualizado pelo progresso
          );
        } else {
          console.warn('Gerenciador de progresso da auditoria não encontrado');
          showSuccessMessage('Auditoria iniciada com sucesso!');

          // Recarregar o dashboard após um tempo
          setTimeout(() => carregarDashboardAuditoria(tipoTributo), 3000);
        }
      } else {
        // Mostrar mensagem de erro
        const errorMsg = data.message || 'Erro ao executar auditoria';
        console.error('Erro retornado pelo servidor:', errorMsg);
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      console.error('Erro ao executar auditoria:', error);
      showErrorMessage(`Erro ao executar auditoria: ${error.message}`);
    });
}

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - auditoria_dashboard.js');

  // Verificar se estamos na página de auditoria
  const path = window.location.pathname;
  if (path.match(/\/auditoria\/(entrada|saida)\/([a-z_-]+)/)) {
    console.log('Detectada página de auditoria específica');

    // Extrair tipo de tributo da URL
    const tipoTributo = path.split('/').pop();
    console.log('Tipo de tributo:', tipoTributo);

    // Carregar dashboard
    carregarDashboardAuditoria(tipoTributo);

    // Garantir que os dropdowns estejam fechados se a sidebar estiver colapsada
    setTimeout(() => {
      if (typeof ensureDropdownsClosedWhenCollapsed === 'function') {
        ensureDropdownsClosedWhenCollapsed();
      }
    }, 100);
  }
});
