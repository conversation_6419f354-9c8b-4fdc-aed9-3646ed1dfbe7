from models import db, Tributo, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL, TributoHistorico, AuditoriaResultado, AuditoriaSumario, NotaFiscalItem
from datetime import datetime
import logging
from sqlalchemy import and_
from sqlalchemy.orm import joinedload
from decimal import Decimal
from services.tributo_calculation_service import TributoCalculationService

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuditoriaService:
    """
    Serviço para auditoria fiscal com base nos cenários em produção
    """

    def __init__(self, empresa_id, usuario_id=None, progress_callback=None):
        """
        Inicializa o serviço de auditoria fiscal

        Args:
            empresa_id (int): ID da empresa
            usuario_id (int, optional): ID do usuário que está realizando a auditoria
            progress_callback (callable, optional): Função callback para reportar progresso
        """
        self.empresa_id = empresa_id
        self.usuario_id = usuario_id
        self.progress_callback = progress_callback

    def executar_auditoria(self, tributo_ids=None, produto_ids=None, cliente_ids=None, forcar_recalculo=False, tipo_tributo=None):
        """
        Executa a auditoria fiscal para os tributos especificados

        Args:
            tributo_ids (list, optional): Lista de IDs de tributos para auditar
            produto_ids (list, optional): Lista de IDs de produtos para auditar
            cliente_ids (list, optional): Lista de IDs de clientes para auditar
            forcar_recalculo (bool, optional): Se True, força o recálculo mesmo para tributos já auditados
            tipo_tributo (str, optional): Tipo de tributo a ser auditado ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
                                         Se None, audita todos os tipos de tributo

        Returns:
            dict: Resultado da auditoria
        """
        logger.info(f"Iniciando auditoria fiscal para empresa_id={self.empresa_id}")
        logger.info(f"Parâmetros: tributo_ids={tributo_ids}, produto_ids={produto_ids}, cliente_ids={cliente_ids}, forcar_recalculo={forcar_recalculo}, tipo_tributo={tipo_tributo}")

        # Se forcar_recalculo for True, limpar sumários existentes para recalcular do zero
        if forcar_recalculo and tipo_tributo:
            self._limpar_sumarios_tipo_tributo(tipo_tributo)

        # Construir query base com carregamento do relacionamento nota_fiscal_item
        query = Tributo.query.options(joinedload(Tributo.nota_fiscal_item)).filter_by(empresa_id=self.empresa_id)

        # Aplicar filtros
        if tributo_ids:
            logger.info(f"Filtrando por tributo_ids: {tributo_ids}")
            query = query.filter(Tributo.id.in_(tributo_ids))
        if produto_ids:
            logger.info(f"Filtrando por produto_ids: {produto_ids}")
            query = query.filter(Tributo.produto_id.in_(produto_ids))
        if cliente_ids:
            logger.info(f"Filtrando por cliente_ids: {cliente_ids}")
            query = query.filter(Tributo.cliente_id.in_(cliente_ids))

        # Filtrar apenas tributos pendentes para o tipo específico, a menos que forcar_recalculo seja True
        if not forcar_recalculo:
            if tipo_tributo == 'icms':
                query = query.filter(Tributo.auditoria_icms_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria ICMS diferente de 'realizada'")
            elif tipo_tributo == 'icms_st':
                query = query.filter(Tributo.auditoria_icms_st_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria ICMS-ST diferente de 'realizada'")
            elif tipo_tributo == 'ipi':
                query = query.filter(Tributo.auditoria_ipi_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria IPI diferente de 'realizada'")
            elif tipo_tributo == 'pis':
                query = query.filter(Tributo.auditoria_pis_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria PIS diferente de 'realizada'")
            elif tipo_tributo == 'cofins':
                query = query.filter(Tributo.auditoria_cofins_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria COFINS diferente de 'realizada'")
            elif tipo_tributo == 'difal':
                query = query.filter(Tributo.auditoria_difal_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria DIFAL diferente de 'realizada'")
            else:
                # Se não especificar tipo, usa o status geral (para compatibilidade)
                query = query.filter(Tributo.auditoria_status != 'realizada')
                logger.info("Filtrando apenas tributos com status de auditoria geral diferente de 'realizada'")
        else:
            logger.info("Forçando recálculo para todos os tributos, incluindo os já auditados")

        # Buscar tributos
        tributos = query.all()
        logger.info(f"Encontrados {len(tributos)} tributos para auditoria")

        # Logar detalhes dos tributos encontrados para diagnóstico
        if tributos:
            for i, tributo in enumerate(tributos[:5]):  # Limitar a 5 para não sobrecarregar o log
                logger.info(f"Tributo {i+1}: ID={tributo.id}, produto_id={tributo.produto_id}, cliente_id={tributo.cliente_id}, tipo_operacao={tributo.tipo_operacao}")
            if len(tributos) > 5:
                logger.info(f"... e mais {len(tributos) - 5} tributos")

        if not tributos:
            logger.warning("Nenhum tributo encontrado para os filtros especificados")
            return {
                'success': False,
                'message': 'Nenhum tributo encontrado para os filtros especificados'
            }

        # Inicializar resultados
        results = {
            'success': True,
            'total': len(tributos),
            'auditados': 0,
            'nao_auditados': 0,
            'por_tipo': {
                'icms': {'auditados': 0, 'nao_auditados': 0},
                'icms_st': {'auditados': 0, 'nao_auditados': 0},
                'ipi': {'auditados': 0, 'nao_auditados': 0},
                'pis': {'auditados': 0, 'nao_auditados': 0},
                'cofins': {'auditados': 0, 'nao_auditados': 0},
                'difal': {'auditados': 0, 'nao_auditados': 0}
            }
        }

        # Processar cada tributo
        for index, tributo in enumerate(tributos):
            logger.info(f"Processando tributo ID={tributo.id}, produto_id={tributo.produto_id}, cliente_id={tributo.cliente_id} ({index + 1}/{len(tributos)})")

            # Reportar progresso se callback estiver disponível
            if self.progress_callback:
                progress_data = {
                    'processed': index,
                    'total': len(tributos),
                    'current_tributo_id': tributo.id,
                    'percentage': round((index / len(tributos)) * 100, 1),
                    'auditados': results['auditados'],
                    'nao_auditados': results['nao_auditados'],
                    'tipo_tributo': tipo_tributo or 'todos'
                }
                logger.info(f"Enviando progresso da auditoria: {progress_data}")
                self.progress_callback(progress_data)

            try:
                # Inicializar variáveis de controle
                ipi_auditado = False
                icms_auditado = False
                icms_st_auditado = False
                pis_auditado = False
                cofins_auditado = False
                difal_auditado = False

                # Se tipo_tributo for None ou 'ipi', auditar IPI
                if tipo_tributo is None or tipo_tributo == 'ipi':
                    # Primeiro, calcular IPI, pois ICMS e ICMS-ST podem depender dele
                    logger.info(f"Iniciando auditoria de IPI para tributo ID={tributo.id}")
                    ipi_auditado = self._auditar_ipi(tributo)
                    logger.info(f"Auditoria de IPI concluída: {ipi_auditado}")

                    if ipi_auditado:
                        # Atualizar status específico de auditoria IPI
                        tributo.auditoria_ipi_status = 'realizada'
                        tributo.auditoria_ipi_data = datetime.now()
                        logger.info(f"Status de auditoria IPI atualizado para 'realizada' para tributo ID={tributo.id}")

                # Se tipo_tributo for None ou 'icms', auditar ICMS
                if tipo_tributo is None or tipo_tributo == 'icms':
                    # Se for auditar ICMS e IPI não foi auditado agora, mas precisa do valor do IPI
                    if not ipi_auditado and tipo_tributo == 'icms':
                        # Verificar se IPI já foi auditado anteriormente
                        if tributo.auditoria_ipi_status == 'realizada' and tributo.cenario_ipi_valor is not None:
                            logger.info(f"Usando valor de IPI já auditado anteriormente para tributo ID={tributo.id}")
                            ipi_auditado = True

                    # Depois, calcular ICMS
                    logger.info(f"Iniciando auditoria de ICMS para tributo ID={tributo.id}")
                    icms_auditado = self._auditar_icms(tributo)
                    logger.info(f"Auditoria de ICMS concluída: {icms_auditado}")

                    if icms_auditado:
                        # Atualizar status específico de auditoria ICMS
                        tributo.auditoria_icms_status = 'realizada'
                        tributo.auditoria_icms_data = datetime.now()
                        logger.info(f"Status de auditoria ICMS atualizado para 'realizada' para tributo ID={tributo.id}")

                # Se tipo_tributo for None ou 'icms_st', auditar ICMS-ST
                if tipo_tributo is None or tipo_tributo == 'icms_st':
                    # Se for auditar ICMS-ST e IPI/ICMS não foram auditados agora, mas precisa dos valores
                    if tipo_tributo == 'icms_st':
                        if not ipi_auditado and tributo.auditoria_ipi_status == 'realizada' and tributo.cenario_ipi_valor is not None:
                            logger.info(f"Usando valor de IPI já auditado anteriormente para tributo ID={tributo.id}")
                            ipi_auditado = True
                        if not icms_auditado and tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                            logger.info(f"Usando valor de ICMS já auditado anteriormente para tributo ID={tributo.id}")
                            icms_auditado = True

                    # Depois, calcular ICMS-ST
                    logger.info(f"Iniciando auditoria de ICMS-ST para tributo ID={tributo.id}")
                    icms_st_auditado = self._auditar_icms_st(tributo)
                    logger.info(f"Auditoria de ICMS-ST concluída: {icms_st_auditado}")

                    if icms_st_auditado:
                        # Atualizar status específico de auditoria ICMS-ST
                        tributo.auditoria_icms_st_status = 'realizada'
                        tributo.auditoria_icms_st_data = datetime.now()
                        logger.info(f"Status de auditoria ICMS-ST atualizado para 'realizada' para tributo ID={tributo.id}")

                # Se tipo_tributo for None ou 'pis', auditar PIS
                if tipo_tributo is None or tipo_tributo == 'pis':
                    # Se for auditar PIS e ICMS não foi auditado agora, mas precisa do valor do ICMS
                    if not icms_auditado and tipo_tributo == 'pis':
                        if tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                            logger.info(f"Usando valor de ICMS já auditado anteriormente para tributo ID={tributo.id}")
                            icms_auditado = True

                    # Calcular PIS
                    logger.info(f"Iniciando auditoria de PIS para tributo ID={tributo.id}")
                    pis_auditado = self._auditar_pis(tributo, icms_auditado)
                    logger.info(f"Auditoria de PIS concluída: {pis_auditado}")

                    if pis_auditado:
                        # Atualizar status específico de auditoria PIS
                        tributo.auditoria_pis_status = 'realizada'
                        tributo.auditoria_pis_data = datetime.now()
                        logger.info(f"Status de auditoria PIS atualizado para 'realizada' para tributo ID={tributo.id}")

                # Se tipo_tributo for None ou 'cofins', auditar COFINS
                if tipo_tributo is None or tipo_tributo == 'cofins':
                    # Se for auditar COFINS e ICMS não foi auditado agora, mas precisa do valor do ICMS
                    if not icms_auditado and tipo_tributo == 'cofins':
                        if tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                            logger.info(f"Usando valor de ICMS já auditado anteriormente para tributo ID={tributo.id}")
                            icms_auditado = True

                    # Calcular COFINS
                    logger.info(f"Iniciando auditoria de COFINS para tributo ID={tributo.id}")
                    cofins_auditado = self._auditar_cofins(tributo, icms_auditado)
                    logger.info(f"Auditoria de COFINS concluída: {cofins_auditado}")

                    if cofins_auditado:
                        # Atualizar status específico de auditoria COFINS
                        tributo.auditoria_cofins_status = 'realizada'
                        tributo.auditoria_cofins_data = datetime.now()
                        logger.info(f"Status de auditoria COFINS atualizado para 'realizada' para tributo ID={tributo.id}")

                # Se tipo_tributo for None ou 'difal', auditar DIFAL
                if tipo_tributo is None or tipo_tributo == 'difal':
                    # Calcular DIFAL
                    logger.info(f"Iniciando auditoria de DIFAL para tributo ID={tributo.id}")
                    difal_auditado = self._auditar_difal(tributo)
                    logger.info(f"Auditoria de DIFAL concluída: {difal_auditado}")

                    if difal_auditado:
                        # Atualizar status específico de auditoria DIFAL
                        tributo.auditoria_difal_status = 'realizada'
                        tributo.auditoria_difal_data = datetime.now()
                        logger.info(f"Status de auditoria DIFAL atualizado para 'realizada' para tributo ID={tributo.id}")

                # Atualizar status geral de auditoria (para compatibilidade)
                if tipo_tributo is None and (ipi_auditado or icms_auditado or icms_st_auditado or pis_auditado or cofins_auditado or difal_auditado):
                    tributo.auditoria_status = 'realizada'
                    tributo.auditoria_data = datetime.now()
                    logger.info(f"Status geral de auditoria atualizado para 'realizada' para tributo ID={tributo.id}")
            except Exception as e:
                logger.error(f"Erro ao auditar tributo ID={tributo.id}: {str(e)}")
                # Continuar com o próximo tributo

            # Atualizar contadores
            if ipi_auditado:
                results['por_tipo']['ipi']['auditados'] += 1
            else:
                results['por_tipo']['ipi']['nao_auditados'] += 1

            if icms_auditado:
                results['por_tipo']['icms']['auditados'] += 1
            else:
                results['por_tipo']['icms']['nao_auditados'] += 1

            if icms_st_auditado:
                results['por_tipo']['icms_st']['auditados'] += 1
            else:
                results['por_tipo']['icms_st']['nao_auditados'] += 1

            if pis_auditado:
                results['por_tipo']['pis']['auditados'] += 1
            else:
                results['por_tipo']['pis']['nao_auditados'] += 1

            if cofins_auditado:
                results['por_tipo']['cofins']['auditados'] += 1
            else:
                results['por_tipo']['cofins']['nao_auditados'] += 1

            if difal_auditado:
                results['por_tipo']['difal']['auditados'] += 1
            else:
                results['por_tipo']['difal']['nao_auditados'] += 1

            # Verificar se pelo menos um tributo foi auditado
            if ipi_auditado or icms_auditado or icms_st_auditado or pis_auditado or cofins_auditado or difal_auditado:
                results['auditados'] += 1
            else:
                results['nao_auditados'] += 1

        # Salvar alterações
        db.session.commit()

        # Reportar progresso final se callback estiver disponível
        if self.progress_callback:
            final_progress_data = {
                'processed': len(tributos),
                'total': len(tributos),
                'current_tributo_id': None,
                'percentage': 100,
                'auditados': results['auditados'],
                'nao_auditados': results['nao_auditados'],
                'tipo_tributo': tipo_tributo or 'todos',
                'completed': True
            }
            logger.info(f"Enviando progresso final da auditoria: {final_progress_data}")
            self.progress_callback(final_progress_data)

        return results

    def _buscar_cenario_vigente(self, tipo_tributo, tributo):
        """
        Busca o cenário vigente para a data da nota fiscal.
        Primeiro busca cenários em produção, se não encontrar, busca cenários inconsistentes.

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            tributo (Tributo): Objeto do tributo

        Returns:
            tuple: (cenario, status_cenario) onde status_cenario é 'producao' ou 'inconsistente'
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Data de referência (data de emissão ou saída)
        data_referencia = tributo.data_saida if tributo.data_saida else tributo.data_emissao

        if not data_referencia:
            logger.warning(f"Tributo ID={tributo.id} não possui data de emissão ou saída")
            return None, None

        # Obter CFOP da nota fiscal item relacionada ao tributo
        cfop_tributo = None
        if tributo.nota_fiscal_item_id and tributo.nota_fiscal_item:
            cfop_tributo = tributo.nota_fiscal_item.cfop
            logger.info(f"CFOP obtido da nota fiscal: {cfop_tributo}")
        else:
            logger.warning(f"Tributo ID={tributo.id} não possui nota_fiscal_item_id ou relacionamento não carregado")

        logger.info(f"Buscando cenário vigente para tributo ID={tributo.id}, tipo={tipo_tributo}, data_referencia={data_referencia}, CFOP={cfop_tributo}")

        # Primeiro, buscar cenário em produção ativo e vigente
        cenario_producao = self._buscar_cenario_por_status(CenarioModel, tributo, 'producao', cfop_tributo, data_referencia)

        if cenario_producao:
            logger.info(f"Cenário em produção encontrado: ID={cenario_producao.id}, CFOP={cenario_producao.cfop}")
            return cenario_producao, 'producao'

        # Se não encontrou cenário em produção, buscar cenário inconsistente
        logger.info(f"Nenhum cenário em produção encontrado, buscando cenário inconsistente...")
        cenario_inconsistente = self._buscar_cenario_inconsistente(CenarioModel, tributo, cfop_tributo)

        if cenario_inconsistente:
            logger.info(f"Cenário inconsistente encontrado: ID={cenario_inconsistente.id}, CFOP={cenario_inconsistente.cfop}")
            return cenario_inconsistente, 'inconsistente'

        logger.warning(f"Nenhum cenário (produção ou inconsistente) encontrado para tributo ID={tributo.id}")
        return None, None

    def _buscar_cenario_por_status(self, CenarioModel, tributo, status, cfop_tributo, data_referencia):
        """
        Busca cenário por status específico

        Args:
            CenarioModel: Modelo do cenário
            tributo: Objeto do tributo
            status (str): Status do cenário ('producao' ou 'inconsistente')
            cfop_tributo (str): CFOP do tributo
            data_referencia (date): Data de referência

        Returns:
            object: Objeto do cenário encontrado ou None
        """
        query = CenarioModel.query.filter(
            CenarioModel.empresa_id == tributo.empresa_id,
            CenarioModel.cliente_id == tributo.cliente_id,
            CenarioModel.produto_id == tributo.produto_id,
            CenarioModel.status == status
        )

        # Para cenários em produção, verificar se está ativo
        if status == 'producao':
            query = query.filter(CenarioModel.ativo == True)

        # Adicionar filtro de CFOP se disponível
        if cfop_tributo:
            query = query.filter(CenarioModel.cfop == cfop_tributo)
            logger.info(f"Filtro de CFOP aplicado: {cfop_tributo}")

        # Adicionar filtro de data de vigência apenas para cenários em produção
        if status == 'producao':
            query = query.filter(
                # Início da vigência é nulo OU data de referência é posterior/igual ao início da vigência
                ((CenarioModel.data_inicio_vigencia == None) | (CenarioModel.data_inicio_vigencia <= data_referencia)),
                # Fim da vigência é nulo OU data de referência é anterior/igual ao fim da vigência
                ((CenarioModel.data_fim_vigencia == None) | (CenarioModel.data_fim_vigencia >= data_referencia))
            )

        # Executar a consulta
        cenario = query.first()

        if cenario:
            logger.info(f"Cenário {status} encontrado: ID={cenario.id}, CFOP={cenario.cfop}")
        else:
            logger.info(f"Nenhum cenário {status} encontrado para os critérios especificados")

        return cenario

    def _buscar_cenario_inconsistente(self, CenarioModel, tributo, cfop_tributo):
        """
        Busca cenário inconsistente (status que começa com 'incons')

        Args:
            CenarioModel: Modelo do cenário
            tributo: Objeto do tributo
            cfop_tributo (str): CFOP do tributo

        Returns:
            object: Objeto do cenário encontrado ou None
        """
        query = CenarioModel.query.filter(
            CenarioModel.empresa_id == tributo.empresa_id,
            CenarioModel.cliente_id == tributo.cliente_id,
            CenarioModel.produto_id == tributo.produto_id,
            CenarioModel.status.like('incons%')  # Status que começa com 'incons'
        )

        # Adicionar filtro de CFOP se disponível
        if cfop_tributo:
            query = query.filter(CenarioModel.cfop == cfop_tributo)
            logger.info(f"Filtro de CFOP aplicado para cenário inconsistente: {cfop_tributo}")

        # Executar a consulta
        cenario = query.first()

        if cenario:
            logger.info(f"Cenário inconsistente encontrado: ID={cenario.id}, CFOP={cenario.cfop}, status={cenario.status}")
        else:
            logger.info(f"Nenhum cenário inconsistente encontrado para os critérios especificados")

        return cenario

    def _auditar_ipi(self, tributo):
        """
        Audita o IPI para um tributo

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('ipi', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de IPI vigente encontrado para o tributo {tributo.id}")
            return False

        # Calcular base de cálculo e valor do IPI usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None
        base_calculo, valor_ipi = TributoCalculationService.calcular_ipi(valor_total, cenario, valor_frete, valor_desconto)

        # Atualizar valores no tributo
        tributo.cenario_ipi_id = cenario.id
        tributo.cenario_ipi_vbc = float(base_calculo)
        tributo.cenario_ipi_valor = float(valor_ipi)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.ipi_valor is not None:
                valor_nota = Decimal(str(tributo.ipi_valor))
                valor_calculado = Decimal(str(valor_ipi))

                # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
                if cenario_status == 'inconsistente':
                    status = 'inconsistente'
                else:
                    # Tolerância de 0.01 para diferenças de arredondamento
                    if abs(valor_nota - valor_calculado) <= Decimal('0.01'):
                        status = 'conforme'
                    else:
                        status = 'inconsistente'

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='ipi'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = float(tributo.ipi_vbc) if tributo.ipi_vbc else None
                    resultado_existente.base_calculo_calculada = float(base_calculo)
                    resultado_existente.status = status
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='ipi',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=float(tributo.ipi_vbc) if tributo.ipi_vbc else None,
                        base_calculo_calculada=float(base_calculo),
                        status=status,
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now()
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'ipi', status)

        logger.info(f"Auditoria de IPI realizada para o tributo {tributo.id}: BC={base_calculo}, Valor={valor_ipi}")

        return True

    def _auditar_icms(self, tributo):
        """
        Audita o ICMS para um tributo

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('icms', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de ICMS vigente encontrado para o tributo {tributo.id}")
            return False

        # Verificar se é cliente de uso e consumo ou ativo imobilizado
        cliente_uso_consumo_ativo = False
        if tributo.cliente and (
            (hasattr(tributo.cliente, 'destinacao') and tributo.cliente.destinacao in ['Uso e Consumo', 'Ativo Imobilizado']) or
            (hasattr(tributo.cliente, 'ind_final') and tributo.cliente.ind_final == '1')  # Consumidor final
        ):
            cliente_uso_consumo_ativo = True

        # Calcular base de cálculo e valor do ICMS usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_icms = TributoCalculationService.calcular_icms(
            valor_total,
            cenario,
            valor_ipi,
            cliente_uso_consumo_ativo,
            valor_frete,
            valor_desconto
        )

        # Atualizar valores no tributo
        tributo.cenario_icms_id = cenario.id
        tributo.cenario_icms_vbc = float(base_calculo)
        tributo.cenario_icms_valor = float(valor_icms)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.icms_valor is not None:
                valor_nota = Decimal(str(tributo.icms_valor))
                valor_calculado = Decimal(str(valor_icms))
                base_calculo_nota_valor = float(tributo.icms_vbc) if tributo.icms_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                # Usar nova lógica de comparação expandida
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'icms',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='icms'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()

                    # Atualizar campos de comparação fiscal
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.origem_nota = comparacao['origem_nota']
                    resultado_existente.origem_cenario = comparacao['origem_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_origem = comparacao['inconsistencia_origem']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='icms',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),

                        # Campos de comparação fiscal
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        origem_nota=comparacao['origem_nota'],
                        origem_cenario=comparacao['origem_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_origem=comparacao['inconsistencia_origem'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'icms', comparacao['status'])

        logger.info(f"Auditoria de ICMS realizada para o tributo {tributo.id}: BC={base_calculo}, Valor={valor_icms}")

        return True

    def _auditar_icms_st(self, tributo):
        """
        Audita o ICMS-ST para um tributo

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('icms_st', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de ICMS-ST vigente encontrado para o tributo {tributo.id}")
            return False

        # Verificar se é cliente de uso e consumo ou ativo imobilizado
        cliente_uso_consumo_ativo = False
        if tributo.cliente and (
            (hasattr(tributo.cliente, 'destinacao') and tributo.cliente.destinacao in ['Uso e Consumo', 'Ativo Imobilizado']) or
            (hasattr(tributo.cliente, 'ind_final') and tributo.cliente.ind_final == '1')  # Consumidor final
        ):
            cliente_uso_consumo_ativo = True

        # Calcular base de cálculo e valor do ICMS-ST usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_icms_st = TributoCalculationService.calcular_icms_st(
            valor_total,
            cenario,
            valor_ipi,
            valor_icms,
            cliente_uso_consumo_ativo,
            valor_frete,
            valor_desconto
        )

        # Atualizar valores no tributo
        tributo.cenario_icms_st_id = cenario.id
        tributo.cenario_icms_st_vbc = float(base_calculo)
        tributo.cenario_icms_st_valor = float(valor_icms_st)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.icms_st_valor is not None:
                valor_nota = Decimal(str(tributo.icms_st_valor))
                valor_calculado = Decimal(str(valor_icms_st))

                # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
                if cenario_status == 'inconsistente':
                    status = 'inconsistente'
                else:
                    # Tolerância de 0.01 para diferenças de arredondamento
                    if abs(valor_nota - valor_calculado) <= Decimal('0.01'):
                        status = 'conforme'
                    else:
                        status = 'inconsistente'

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='icms_st'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = float(tributo.icms_st_vbc) if tributo.icms_st_vbc else None
                    resultado_existente.base_calculo_calculada = float(base_calculo)
                    resultado_existente.status = status
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='icms_st',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=float(tributo.icms_st_vbc) if tributo.icms_st_vbc else None,
                        base_calculo_calculada=float(base_calculo),
                        status=status,
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now()
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'icms_st', status)

        logger.info(f"Auditoria de ICMS-ST realizada para o tributo {tributo.id}: BC={base_calculo}, Valor={valor_icms_st}")

        return True

    def _auditar_pis(self, tributo, icms_auditado):
        """
        Audita o PIS para um tributo

        Args:
            tributo (Tributo): Objeto do tributo
            icms_auditado (bool): Indica se o ICMS foi auditado

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('pis', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de PIS vigente encontrado para o tributo {tributo.id}")
            return False

        # Calcular base de cálculo e valor do PIS usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor and icms_auditado else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_pis = TributoCalculationService.calcular_pis(
            valor_total,
            cenario,
            valor_icms,
            valor_frete,
            valor_desconto
        )

        # Atualizar valores no tributo
        tributo.cenario_pis_id = cenario.id
        tributo.cenario_pis_vbc = float(base_calculo)
        tributo.cenario_pis_valor = float(valor_pis)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.pis_valor is not None:
                valor_nota = Decimal(str(tributo.pis_valor))
                valor_calculado = Decimal(str(valor_pis))

                # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
                if cenario_status == 'inconsistente':
                    status = 'inconsistente'
                else:
                    # Tolerância de 0.01 para diferenças de arredondamento
                    if abs(valor_nota - valor_calculado) <= Decimal('0.01'):
                        status = 'conforme'
                    else:
                        status = 'inconsistente'

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='pis'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = float(tributo.pis_vbc) if tributo.pis_vbc else None
                    resultado_existente.base_calculo_calculada = float(base_calculo)
                    resultado_existente.status = status
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='pis',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=float(tributo.pis_vbc) if tributo.pis_vbc else None,
                        base_calculo_calculada=float(base_calculo),
                        status=status,
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now()
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'pis', status)

        logger.info(f"Auditoria de PIS realizada para o tributo {tributo.id}: BC={base_calculo}, Valor={valor_pis}")

        return True

    def _auditar_cofins(self, tributo, icms_auditado):
        """
        Audita o COFINS para um tributo

        Args:
            tributo (Tributo): Objeto do tributo
            icms_auditado (bool): Indica se o ICMS foi auditado

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('cofins', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de COFINS vigente encontrado para o tributo {tributo.id}")
            return False

        # Calcular base de cálculo e valor do COFINS usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor and icms_auditado else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_cofins = TributoCalculationService.calcular_cofins(
            valor_total,
            cenario,
            valor_icms,
            valor_frete,
            valor_desconto
        )

        # Atualizar valores no tributo
        tributo.cenario_cofins_id = cenario.id
        tributo.cenario_cofins_vbc = float(base_calculo)
        tributo.cenario_cofins_valor = float(valor_cofins)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.cofins_valor is not None:
                valor_nota = Decimal(str(tributo.cofins_valor))
                valor_calculado = Decimal(str(valor_cofins))

                # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
                if cenario_status == 'inconsistente':
                    status = 'inconsistente'
                else:
                    # Tolerância de 0.01 para diferenças de arredondamento
                    if abs(valor_nota - valor_calculado) <= Decimal('0.01'):
                        status = 'conforme'
                    else:
                        status = 'inconsistente'

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='cofins'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = float(tributo.cofins_vbc) if tributo.cofins_vbc else None
                    resultado_existente.base_calculo_calculada = float(base_calculo)
                    resultado_existente.status = status
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='cofins',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=float(tributo.cofins_vbc) if tributo.cofins_vbc else None,
                        base_calculo_calculada=float(base_calculo),
                        status=status,
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now()
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'cofins', status)

        logger.info(f"Auditoria de COFINS realizada para o tributo {tributo.id}: BC={base_calculo}, Valor={valor_cofins}")

        return True

    def _auditar_difal(self, tributo):
        """
        Audita o DIFAL para um tributo

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Buscar cenário vigente
        cenario, cenario_status = self._buscar_cenario_vigente('difal', tributo)

        if not cenario:
            logger.info(f"Nenhum cenário de DIFAL vigente encontrado para o tributo {tributo.id}")
            return False

        # Calcular valor do DIFAL usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')

        valor_difal = TributoCalculationService.calcular_difal(valor_total, cenario)

        # Atualizar valores no tributo
        tributo.cenario_difal_id = cenario.id
        tributo.cenario_difal_valor = float(valor_difal)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.difal_v_icms_uf_dest is not None:
                valor_nota = Decimal(str(tributo.difal_v_icms_uf_dest))
                valor_calculado = Decimal(str(valor_difal))

                # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
                if cenario_status == 'inconsistente':
                    status = 'inconsistente'
                else:
                    # Tolerância de 0.01 para diferenças de arredondamento
                    if abs(valor_nota - valor_calculado) <= Decimal('0.01'):
                        status = 'conforme'
                    else:
                        status = 'inconsistente'

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='difal'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = float(tributo.difal_vbcuf_dest) if tributo.difal_vbcuf_dest else None
                    resultado_existente.base_calculo_calculada = None  # DIFAL não tem base de cálculo separada
                    resultado_existente.status = status
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='difal',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=float(tributo.difal_vbcuf_dest) if tributo.difal_vbcuf_dest else None,
                        base_calculo_calculada=None,  # DIFAL não tem base de cálculo separada
                        status=status,
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now()
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'difal', status)

        logger.info(f"Auditoria de DIFAL realizada para o tributo {tributo.id}: Valor={valor_difal}")

        return True

    def _comparar_campos_fiscais(self, tributo, cenario, tipo_tributo, valor_nota, valor_calculado, base_calculo_nota, base_calculo_calculada, cenario_status='producao'):
        """
        Compara campos fiscais entre nota e cenário para determinar inconsistências

        Args:
            tributo: Objeto do tributo da nota fiscal
            cenario: Objeto do cenário aplicado
            tipo_tributo: Tipo do tributo ('icms', 'icms_st', etc.)
            valor_nota: Valor do tributo na nota
            valor_calculado: Valor calculado pelo cenário
            base_calculo_nota: Base de cálculo da nota
            base_calculo_calculada: Base de cálculo calculada

        Returns:
            dict: Dicionário com informações de comparação e inconsistências
        """
        # Obter campos da nota fiscal
        cst_nota = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_cst", None)
        origem_nota = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_origem", None)

        # Para ICMS e ICMS-ST, usar campos específicos
        if tipo_tributo == 'icms':
            aliquota_nota = float(tributo.icms_aliquota) if tributo.icms_aliquota else None
        elif tipo_tributo == 'icms_st':
            aliquota_nota = float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
        elif tipo_tributo == 'ipi':
            aliquota_nota = float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
        elif tipo_tributo == 'pis':
            aliquota_nota = float(tributo.pis_aliquota) if tributo.pis_aliquota else None
        elif tipo_tributo == 'cofins':
            aliquota_nota = float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
        elif tipo_tributo == 'difal':
            aliquota_nota = float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None
        else:
            aliquota_nota = None

        # Obter campos do cenário
        cst_cenario = cenario.cst if hasattr(cenario, 'cst') else None
        origem_cenario = cenario.origem if hasattr(cenario, 'origem') else None

        # Para ICMS-ST, usar alíquota específica se disponível
        if tipo_tributo == 'icms_st' and hasattr(cenario, 'icms_st_aliquota') and cenario.icms_st_aliquota:
            aliquota_cenario = float(cenario.icms_st_aliquota)
        elif hasattr(cenario, 'aliquota') and cenario.aliquota:
            aliquota_cenario = float(cenario.aliquota)
        else:
            aliquota_cenario = None

        # Comparar campos com tolerância para valores numéricos
        tolerancia_valor = Decimal('0.01')
        tolerancia_aliquota = Decimal('0.0001')  # Tolerância menor para alíquotas

        # Verificar inconsistências
        inconsistencia_valor = abs(Decimal(str(valor_nota)) - Decimal(str(valor_calculado))) > tolerancia_valor
        inconsistencia_cst = cst_nota != cst_cenario
        inconsistencia_origem = origem_nota != origem_cenario

        inconsistencia_aliquota = False
        if aliquota_nota is not None and aliquota_cenario is not None:
            inconsistencia_aliquota = abs(Decimal(str(aliquota_nota)) - Decimal(str(aliquota_cenario))) > tolerancia_aliquota

        inconsistencia_base_calculo = False
        if base_calculo_nota is not None and base_calculo_calculada is not None:
            inconsistencia_base_calculo = abs(Decimal(str(base_calculo_nota)) - Decimal(str(base_calculo_calculada))) > tolerancia_valor

        # Determinar status geral
        # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
        if cenario_status == 'inconsistente':
            status = 'inconsistente'
        else:
            tem_inconsistencia = (inconsistencia_valor or inconsistencia_cst or
                                 inconsistencia_origem or inconsistencia_aliquota or
                                 inconsistencia_base_calculo)
            status = 'inconsistente' if tem_inconsistencia else 'conforme'

        return {
            'status': status,
            'cst_nota': cst_nota,
            'cst_cenario': cst_cenario,
            'origem_nota': origem_nota,
            'origem_cenario': origem_cenario,
            'aliquota_nota': aliquota_nota,
            'aliquota_cenario': aliquota_cenario,
            'inconsistencia_valor': inconsistencia_valor,
            'inconsistencia_cst': inconsistencia_cst,
            'inconsistencia_origem': inconsistencia_origem,
            'inconsistencia_aliquota': inconsistencia_aliquota,
            'inconsistencia_base_calculo': inconsistencia_base_calculo
        }

    def _limpar_sumarios_tipo_tributo(self, tipo_tributo):
        """
        Limpa os sumários de auditoria para um tipo de tributo específico

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        """
        try:
            # Deletar sumários existentes para este tipo de tributo e empresa
            sumarios_deletados = AuditoriaSumario.query.filter_by(
                empresa_id=self.empresa_id,
                tipo_tributo=tipo_tributo
            ).delete()

            logger.info(f"Deletados {sumarios_deletados} sumários existentes para {tipo_tributo}")

            # Deletar resultados de auditoria existentes para este tipo de tributo e empresa
            resultados_deletados = AuditoriaResultado.query.filter_by(
                empresa_id=self.empresa_id,
                tipo_tributo=tipo_tributo
            ).delete()

            logger.info(f"Deletados {resultados_deletados} resultados de auditoria existentes para {tipo_tributo}")

            # Commit das deleções
            db.session.commit()

        except Exception as e:
            logger.error(f"Erro ao limpar sumários para {tipo_tributo}: {str(e)}")
            db.session.rollback()

    def _atualizar_sumario_auditoria(self, tributo, tipo_tributo, status):
        """
        Atualiza o sumário de auditoria para um tipo de tributo

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            status (str): Status da auditoria ('conforme', 'inconsistente')

        Returns:
            bool: True se a atualização foi realizada, False caso contrário
        """
        # Obter o ano e mês da data de emissão
        if not tributo.data_emissao:
            logger.warning(f"Tributo ID={tributo.id} não possui data de emissão")
            return False

        ano = tributo.data_emissao.year
        mes = tributo.data_emissao.month

        # Verificar se já existe um sumário para esta empresa/ano/mês/tipo
        sumario = AuditoriaSumario.query.filter_by(
            empresa_id=tributo.empresa_id,
            ano=ano,
            mes=mes,
            tipo_tributo=tipo_tributo
        ).first()

        # Se não existir, criar um novo
        if not sumario:
            sumario = AuditoriaSumario(
                empresa_id=tributo.empresa_id,
                escritorio_id=tributo.escritorio_id,
                ano=ano,
                mes=mes,
                tipo_tributo=tipo_tributo,
                total_notas=0,
                total_produtos=0,
                valor_total_notas=0,
                valor_total_cenarios=0,
                valor_total_tributo=0,
                total_conforme=0,
                total_inconsistente=0,
                valor_inconsistente_maior=0,
                valor_inconsistente_menor=0
            )
            db.session.add(sumario)
            db.session.flush()  # Obter o ID sem commit

        # Obter o item de nota fiscal
        nota_fiscal_item = NotaFiscalItem.query.get(tributo.nota_fiscal_item_id)
        if not nota_fiscal_item:
            logger.warning(f"Item de nota fiscal ID={tributo.nota_fiscal_item_id} não encontrado")
            return False

        # Atualizar contadores
        if status == 'conforme':
            sumario.total_conforme += 1
        else:
            sumario.total_inconsistente += 1

        # Atualizar total de produtos (deve ser a soma de conforme + inconsistente)
        # Cada item auditado conta como um produto, mesmo que seja o mesmo produto em notas diferentes
        sumario.total_produtos = sumario.total_conforme + sumario.total_inconsistente

        # Atualizar valores
        # Obter as notas já contabilizadas
        notas_contabilizadas = sumario.get_notas_contabilizadas()

        if str(nota_fiscal_item.numero_nf) not in notas_contabilizadas:
            sumario.total_notas += 1
            notas_contabilizadas.add(str(nota_fiscal_item.numero_nf))
            sumario.set_notas_contabilizadas(notas_contabilizadas)

        # Obter valores do tributo da nota fiscal e do cenário calculado
        valor_nota = Decimal('0')
        valor_calculado = Decimal('0')

        if tipo_tributo == 'icms':
            valor_nota = Decimal(str(tributo.icms_valor)) if tributo.icms_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else Decimal('0')
        elif tipo_tributo == 'icms_st':
            valor_nota = Decimal(str(tributo.icms_st_valor)) if tributo.icms_st_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_icms_st_valor)) if tributo.cenario_icms_st_valor else Decimal('0')
        elif tipo_tributo == 'ipi':
            valor_nota = Decimal(str(tributo.ipi_valor)) if tributo.ipi_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else Decimal('0')
        elif tipo_tributo == 'pis':
            valor_nota = Decimal(str(tributo.pis_valor)) if tributo.pis_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_pis_valor)) if tributo.cenario_pis_valor else Decimal('0')
        elif tipo_tributo == 'cofins':
            valor_nota = Decimal(str(tributo.cofins_valor)) if tributo.cofins_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_cofins_valor)) if tributo.cenario_cofins_valor else Decimal('0')
        elif tipo_tributo == 'difal':
            valor_nota = Decimal(str(tributo.difal_v_icms_uf_dest)) if tributo.difal_v_icms_uf_dest else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_difal_valor)) if tributo.cenario_difal_valor else Decimal('0')

        # Atualizar valores totais dos tributos (nota vs cenário)
        sumario.valor_total_notas += valor_nota
        sumario.valor_total_cenarios += valor_calculado

        # Manter compatibilidade com valor_total_tributo (será igual ao valor_total_cenarios)
        sumario.valor_total_tributo = sumario.valor_total_cenarios

        # Atualizar valores inconsistentes (a maior ou a menor)
        if status == 'inconsistente':
            diferenca = valor_calculado - valor_nota
            if diferenca > 0:
                # Valor calculado é maior que o valor da nota (a maior)
                sumario.valor_inconsistente_maior += diferenca
            else:
                # Valor calculado é menor que o valor da nota (a menor)
                sumario.valor_inconsistente_menor += abs(diferenca)

        # Atualizar data de atualização
        sumario.data_atualizacao = datetime.now()

        return True
